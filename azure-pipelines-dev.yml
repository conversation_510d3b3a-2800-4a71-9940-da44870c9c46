# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- dev

pool:
  vmImage: ubuntu-latest

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '16.x'
  displayName: 'Install Node.js'

- task: Bash@3
  inputs:
    targetType: 'inline'
    script: |
      echo "REACT_APP_MLO_SERVER=${MLO_SERVER}" >> ${BUILD_SOURCESDIRECTORY}/.env
      echo "REACT_APP_REDIRECT_URI=${APP_REDIRECT_URI}" >> ${BUILD_SOURCESDIRECTORY}/.env
      echo "REACT_APP_MSAL_AUTH=${MSAL_AUTH}" >> ${BUILD_SOURCESDIRECTORY}/.env
      echo "REACT_APP_MSAL_CLIENTID=${MSAL_CLIENTID}" >> ${BUILD_SOURCESDIRECTORY}/.env
      echo "Build.SourcesDirectory is: ${BUILD_SOURCESDIRECTORY}"
      ls ${BUILD_SOURCESDIRECTORY} -a
      cat ${BUILD_SOURCESDIRECTORY}/.env
  displayName: 'Create env variables'

- script: |
    npm install --force
    npm run build
  displayName: 'npm install and build'

  
- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: 'build'
    ArtifactName: 'build'
    publishLocation: 'Container'