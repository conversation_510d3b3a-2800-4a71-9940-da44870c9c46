{"name": "<PERSON><PERSON><PERSON>", "version": "4.1.0", "private": true, "homepage": "/experience/discover", "dependencies": {"@emotion/styled": "11.12.0", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.4", "@mui/styles": "^5.16.4", "@reduxjs/toolkit": "^2.2.3", "@south-paw/react-vector-maps": "^3.2.0", "@tanstack/match-sorter-utils": "^8.15.1", "@tanstack/react-table": "^8.16.0", "@testing-library/jest-dom": "^6.4.2", "@tippyjs/react": "4.2.6", "@types/react": "^18.2.79", "aos": "^2.3.4", "apexcharts": "^3.48.0", "axios": "^1.6.8", "axios-mock-adapter": "^1.22.0", "bootstrap": "5.3.2", "feather-icons-react": "^0.7.0", "file-saver": "^2.0.5", "firebase": "^10.11.0", "formik": "^2.4.5", "framer-motion": "^11.18.1", "html2canvas": "^1.4.1", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.473.0", "perfect-freehand": "^1.2.0", "prismjs": "^1.29.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.7", "react-color": "^2.19.3", "react-countup": "^6.5.3", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-flatpickr": "^3.10.13", "react-i18next": "^14.1.0", "react-icons": "^4.12.0", "react-loading-indicators": "^1.0.0", "react-markdown": "^9.0.3", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^9.1.1", "react-router-dom": "^6.22.3", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-toastify": "^10.0.5", "react-zoom-pan-pinch": "^3.6.1", "reactstrap": "^9.2.2", "redux": "^5.0.1", "reselect": "^5.1.0", "roughjs": "^4.6.6", "rxjs": "^7.8.1", "simplebar-react": "^3.2.4", "source-map-explorer": "^2.5.3", "swiper": "^11.1.1", "tippy.js": "^6.3.7", "typescript": "^5.4.5", "web-vitals": "^3.5.2", "yup": "^1.4.0"}, "type": "module", "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.14", "@types/prismjs": "^1.26.4", "@types/react": "^18.2.43", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.2.17", "@types/react-flatpickr": "^3.8.11", "sass": "^1.75.0", "typescript": "^5.2.2", "vitest": "^1.1.3", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4"}, "scripts": {"start": " set PORT=4200 && react-scripts start", "build": "node --max_old_space_size=4096 node_modules/react-scripts/scripts/build.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}