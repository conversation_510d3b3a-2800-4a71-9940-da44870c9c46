{
  "compilerOptions": {
    "target": "ES6",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "baseUrl": "./src", // This must be specified if "paths" is.
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src/config.js", "src/pages/ProductDesign/FrontPage/DescriptionInput.tsx", "src/pages/ProductDesign/FrontPage/DeviceOption.tsx", "src/pages/ProductDesign/FrontPage/FirstStep.jsx", "src/pages/ProductDesign/FrontPage/LoadingStep.jsx", "src/pages/ProductDesign/FrontPage/ProjectWizard.jsx", "src/pages/ProductDesign/FrontPage/SecondStep.jsx"],
  "exclude": ["node_modules"]
}
