  export interface LoginResponse {
        loginUrl: string;
      }

      export interface TokenResponse {
        token_type: string;
        scope: string;
        expires_in: number;
        access_token: string;
        id_token: string;
        refresh_token: string;
        user_name: string;
        email: string;
      }

      export interface TokenPair {
        accessToken: string;
        refreshToken: string;
      }

      export interface User {
        name: string | null;
      }

      export interface AuthContextType {
        isAuthenticated: boolean;
        user: User | null;
        isLoading: boolean;
        login: () => Promise<void>;
        logout: () => Promise<void>;
        handleLoginSuccess: () => void;
      }