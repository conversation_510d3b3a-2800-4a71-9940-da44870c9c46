 import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
      import config from '../config';
      import { getAccessToken, storeAccessToken, clearTokens }
      from '../auth/tokenStorage';

      import { refreshToken as refreshAuthToken } from '../auth/authService';


      const api = axios.create({
        baseURL: config.auth.API_AUTH_URL,
      });


      // Add a custom property to the request config for retry logic

      interface CustomInternalAxiosRequestConfig extends
      InternalAxiosRequestConfig {
        _retry?: boolean;
      }


      // Request interceptor

      api.interceptors.request.use(
        (config: InternalAxiosRequestConfig) => {
          const token = getAccessToken();
          if (token) {
            config.headers['access-key'] = token.trim();
          }
          return config;
        },
        (error: AxiosError) => Promise.reject(error)
      );


      let isRefreshing = false;

      let failedQueue: { resolve: (value: unknown) => void; reject: (reason?:
      any) => void; }[] = [];


      const processQueue = (error: Error | null, token: string | null = null) =>
      {
        failedQueue.forEach(prom => {
          if (error) {
            prom.reject(error);
          } else {
            prom.resolve(token);
          }
        });
        failedQueue = [];
      };


      // Response interceptor

      api.interceptors.response.use(
        (response) => response,
        async (error: AxiosError) => {
          const originalRequest = error.config as CustomInternalAxiosRequestConfig;

          if (error.response?.status === 401 && !originalRequest._retry) {
            if (isRefreshing) {
              return new Promise((resolve, reject) => {
                failedQueue.push({ resolve, reject });
              }).then(token => {
                if (originalRequest.headers) {
                   originalRequest.headers['access-key'] = token as string;
                }
                return api(originalRequest);
              });
            }

            originalRequest._retry = true;
            isRefreshing = true;

            try {
              const { accessToken: newAccessToken } = await refreshAuthToken();
              storeAccessToken(newAccessToken);
              if (originalRequest.headers) {
                originalRequest.headers['access-key'] = newAccessToken;
              }
              processQueue(null, newAccessToken);
              return api(originalRequest);
            } catch (refreshError: any) {
              processQueue(refreshError, null);
              clearTokens();
              window.location.href = '/login';
              return Promise.reject(refreshError);
            } finally {
              isRefreshing = false;
            }
          }

          return Promise.reject(error);
        }
      );


      export default api;