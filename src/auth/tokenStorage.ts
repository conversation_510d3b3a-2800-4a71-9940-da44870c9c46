
  const ACCESS_TOKEN_KEY = 'access_token';

  const REFRESH_TOKEN_KEY = 'refresh_token';

  const NAME_KEY = 'name';

  const USERNAME_KEY = 'username';

  const ID_TOKEN = 'id_token';


  const getCurrentDomain = (): string => {
    return window.location.hostname;
  };


  export const setCookie = (cname: string, cvalue: string, exseconds?: number):
  void => {
    const d = new Date();
    let expires = '';
    if (exseconds) {
      d.setTime(d.getTime() + exseconds * 1000);
      expires = 'expires=' + d.toUTCString();
    }
    const secureFlag = window.location.protocol === 'https:' ? ';Secure' : '';
    const sameSiteFlag = ';SameSite=Lax';
    const domainFlag = `;domain=${getCurrentDomain()}`;
    document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;
  };


  const getCookie = (name: string): string | null => {
    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
    return match ? decodeURIComponent(match[2]) : null;
  };


  export const deleteCookie = (name: string): void => {
    const domain = getCurrentDomain();
    const isLocalhost = domain === 'localhost' || domain === '127.0.0.1';

    // For localhost, don't use domain flag as it can cause issues
    const domainFlag = isLocalhost ? '' : `;domain=${domain}`;
    const secureFlag = window.location.protocol === 'https:' ? ';Secure' : '';

    // Try multiple deletion approaches to ensure cookies are cleared
    document.cookie = `${name}=; path=/; max-age=0${secureFlag}; SameSite=Lax${domainFlag}`;
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT${secureFlag}; SameSite=Lax${domainFlag}`;

    // Also try without domain for localhost
    if (!isLocalhost) {
      document.cookie = `${name}=; path=/; max-age=0${secureFlag}; SameSite=Lax`;
      document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT${secureFlag}; SameSite=Lax`;
    }

    console.log(`Deleted cookie: ${name}`);
  };


  export const storeTokens = (accessToken: string, refreshToken: string,
  expiresInSeconds: number): void => {
    setCookie(ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
    setCookie(REFRESH_TOKEN_KEY, refreshToken);
  };


  export const storeAccessToken = (accessToken: string, expiresInSeconds?:
  number): void => {
    setCookie(ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
  };


  export const storeInfo = (name: string, username: string, idToken: string):
  void => {
    setCookie(NAME_KEY, name);
    setCookie(USERNAME_KEY, username);
    setCookie(ID_TOKEN, idToken);
  };


  export const getAccessToken = (): string | null =>
  getCookie(ACCESS_TOKEN_KEY);

  export const getRefreshToken = (): string | null =>
  getCookie(REFRESH_TOKEN_KEY);

  export const getName = (): string | null => getCookie(NAME_KEY);

  export const getUsername = (): string | null => getCookie(USERNAME_KEY);

  export const getIdToken = (): string | null => getCookie(ID_TOKEN);


  export const clearTokens = (): void => {
    console.log('Clearing all tokens and cookies...');
    console.log('Before clearing - cookies:', document.cookie);

    deleteCookie(ACCESS_TOKEN_KEY);
    deleteCookie(REFRESH_TOKEN_KEY);
    deleteCookie(NAME_KEY);
    deleteCookie(USERNAME_KEY);
    deleteCookie(ID_TOKEN);

    // Also clear localStorage as a backup
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(NAME_KEY);
    localStorage.removeItem(USERNAME_KEY);
    localStorage.removeItem(ID_TOKEN);

    console.log('After clearing - cookies:', document.cookie);
    console.log('Tokens cleared successfully');

    // Dispatch custom event to notify UserContext of auth state change
    window.dispatchEvent(new CustomEvent('authStateChanged'));
  };