import api from "../api/api";
import { getApiEndpoints } from "../config";
import { LoginResponse, TokenResponse, TokenPair } from "../types/auth.types";
import {
  storeTokens,
  storeInfo,
  getIdToken,
  clearTokens,
  getRefreshToken,
} from "./tokenStorage";

const getAuthHeaders = () => ({
  Authorization: `Basic YWRtaW46YWRtaW4xMjM=`,
});

export const login = async (redirectUrl: string): Promise<LoginResponse> => {
  const endpoints = getApiEndpoints();
  const url = `${endpoints.auth.login}?redirectUrl=${redirectUrl}`;
  const response = await api.get<LoginResponse>(url, {
    headers: getAuthHeaders(),
  });
  window.location.href = response.data.loginUrl;
  return response.data;
};

export const exchangeCodeForToken = async (
  code: string,
  redirectUrl: string
): Promise<TokenPair> => {
  const endpoints = getApiEndpoints();
  const url = `${endpoints.auth.token}?redirectUrl=${redirectUrl}`;
  const response = await api.post<TokenResponse>(
    url,
    { code },
    { headers: getAuthHeaders() }
  );
  const {
    access_token,
    refresh_token,
    expires_in,
    user_name,
    email,
    id_token,
  } = response.data;

  storeTokens(access_token, refresh_token, expires_in);
  storeInfo(user_name, email, id_token);

  return { accessToken: access_token, refreshToken: refresh_token };
};

export const refreshToken = async (token?: string): Promise<TokenPair> => {
  const refreshTokenToUse = token || getRefreshToken();
  const endpoints = getApiEndpoints();
  const response = await api.post<TokenResponse>(
    endpoints.auth.refresh,
    { refreshToken: refreshTokenToUse },
    { headers: getAuthHeaders() }
  );

  const {
    access_token,
    refresh_token,
    expires_in,
    user_name,
    email,
    id_token,
  } = response.data;

  storeTokens(access_token, refresh_token, expires_in);
  storeInfo(user_name, email, id_token);

  return { accessToken: access_token, refreshToken: refresh_token };
};

export const clearLocalTokens = (): void => {
  clearTokens();
};

export const logout = async (redirectUrl?: string): Promise<void> => {
  const idToken = getIdToken();
  const endpoints = getApiEndpoints();
  const url = `${endpoints.auth.logout}?redirectUrl=${redirectUrl}`;
  const headers = { ...getAuthHeaders(), "X-ID-TOKEN": idToken || "" };

  try {
    console.log("Calling backend logout endpoint:", url);
    const response = await api.get<{ logoutUrl: string }>(url, { headers });

    console.log("Backend logout response:", response.data);

    // Clear tokens after successful backend call (like Angular tap operator)
    clearTokens();

    console.log("Redirecting to Azure logout URL:", response.data.logoutUrl);

    // Redirect to the Azure logout URL provided by backend
    window.location.href = response.data.logoutUrl;
  } catch (error) {
    console.error("Logout failed:", error);

    // Clear tokens even if backend call fails
    clearTokens();

    // Fallback: redirect to app login page if redirectUrl is provided
    if (redirectUrl) {
      console.log("Falling back to local logout, redirecting to:", redirectUrl);
      window.location.href = redirectUrl;
    }

    throw error; // Re-throw error like Angular catchError
  }
};
