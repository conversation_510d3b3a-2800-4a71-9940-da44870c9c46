import React, { createContext, useState, useEffect, ReactNode } from "react";
import { getAccessToken, getUsername } from "./tokenStorage";
import * as authService from "./authService";
import config, { getAuthUrls } from "../config";
import { User, AuthContextType } from "../types/auth.types";

export const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check authentication status on mount
    const checkAuthStatus = async () => {
      // First check if this is an OAuth callback (has code parameter)
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get("code");
      const refreshTokenParam = urlParams.get("refresh_token");

      if (code || refreshTokenParam) {
        // This is an OAuth callback, process the authentication
        try {
          if (code) {
            await authService.exchangeCodeForToken(
              code,
              config.auth.APP_REDIRECT_URI
            );
          } else if (refreshTokenParam) {
            await authService.refreshToken(refreshTokenParam);
          }

          // Clear the URL parameters
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );

          setIsAuthenticated(true);
          setUser({ name: getUsername() });

          // Notify UserContext of authentication change
          window.dispatchEvent(new CustomEvent('authStateChanged'));
        } catch (error) {
          console.error("OAuth callback failed:", error);
          setIsAuthenticated(false);
          setUser(null);
        }
      } else {
        // Normal authentication check
        const token = getAccessToken();
        const username = getUsername();

        console.log("Checking auth status:", { token: !!token, username });

        if (token) {
          setIsAuthenticated(true);
          setUser({ name: username });

          // Notify UserContext of authentication change
          window.dispatchEvent(new CustomEvent('authStateChanged'));
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      }
      setIsLoading(false);
    };

    checkAuthStatus();
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      setUser({ name: getUsername() });
    } else {
      setUser(null);
    }
  }, [isAuthenticated]);

  const login = async (): Promise<void> => {
    const authUrls = getAuthUrls();
    await authService.login(authUrls.redirectUri);
  };

  const handleLoginSuccess = (): void => {
    setIsAuthenticated(true);
    setUser({ name: getUsername() });

    // Notify UserContext of authentication change
    window.dispatchEvent(new CustomEvent('authStateChanged'));
  };

  const logout = async (): Promise<void> => {
    try {
      console.log("Starting logout process...");

      // Clear local state immediately
      setIsAuthenticated(false);
      setUser(null);

      // Redirect to Azure logout with account selection, then back to login page
      const authUrls = getAuthUrls();
      const redirectUrl = authUrls.loginUrl;

      console.log("Calling Azure logout with redirect to login...");
      await authService.logout(redirectUrl);
    } catch (error) {
      console.error("Logout process failed:", error);
      // The authService.logout already clears tokens and handles fallback redirect
      // Just ensure local state is cleared
      setIsAuthenticated(false);
      setUser(null);

      // Fallback: redirect to login page directly
      const authUrls = getAuthUrls();
      window.location.href = authUrls.loginUrl;
    }
  };

  const value: AuthContextType = {
    isAuthenticated,
    user,
    isLoading,
    login,
    logout,
    handleLoginSuccess,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
