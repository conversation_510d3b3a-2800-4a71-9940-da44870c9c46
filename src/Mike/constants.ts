import config from '../config';

const MLO_SERVER = config.server.MLO_SERVER
const GET_BRAINSTORMER_ENDPOINT = "get-brainstormer"
const UPDATE_BRAINSTORMER_ENDPOINT = "edit-brainstormer"
const ADD_BRAINSTORMER_ENDPOINT = "add-brainstormer"
const NEW_I2C_PROJECT = "projectAdded";
const EXISTING_I2C_PROJECT = "project_Data_History";
const REACT_APP_DEFAULTAUTH = "Admin";
export { MLO_SERVER, GET_BRAINSTORMER_ENDPOINT,UPDATE_BRAINSTORMER_ENDPOINT, ADD_BRAINSTORMER_ENDPOINT,NEW_I2C_PROJECT, EXISTING_I2C_PROJECT, REACT_APP_DEFAULTAUTH }