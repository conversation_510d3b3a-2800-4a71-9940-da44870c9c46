
import React, { useEffect, useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle
} from 'reactstrap'

import { useUser } from '../../context/UserContext'
import { getFirstNameFromEmail } from 'Mike/utils/utils'
import * as authService from '../../auth/authService'

const ProfileDropdown = () => {
  const navigate = useNavigate()
  const { instance } = useUser()

  const [userName, setUserName] = useState('MLO User')
  const [userEmail, setUserEmail] = useState('<EMAIL>')
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  useEffect(() => {
    // Get user info from UserContext
    const activeAccount = instance.getActiveAccount()

    if (activeAccount) {
      setUserEmail(activeAccount.username || 'mi<PERSON><PERSON>@ascendion.com')
      setUserName(activeAccount.name || getFirstNameFromEmail(activeAccount.username || '') || 'MLO User')
    } else {
      // Fallback to default values
      setUserEmail('<EMAIL>')
      setUserName('MLO User')
    }
  }, [instance])

  // Handle logout functionality
  const handleLogout = async (): Promise<void> => {
    if (isLoggingOut) return; // Prevent multiple clicks

    setIsLoggingOut(true)
    console.log('Logout button clicked')

    try {
      // Redirect to login page after Azure logout
      const loginUrl = `${window.location.origin}/experience/discover/login`
      console.log('Starting logout with redirect to login:', loginUrl)

      // Call authService logout directly
      await authService.logout(loginUrl)
      console.log('Logout successful')

    } catch (error) {
      console.error('Logout failed:', error)

      // Clear tokens locally even if logout API fails
      authService.clearLocalTokens()

      // Navigate to login page
      navigate('/login')

    } finally {
      setIsLoggingOut(false)
    }
  }

  //Dropdown Toggle
  const [isProfileDropdown, setIsProfileDropdown] = useState<boolean>(false)
  const toggleProfileDropdown = () => {
    setIsProfileDropdown(!isProfileDropdown)
  }
  return (
    <React.Fragment>
      <Dropdown
        isOpen={isProfileDropdown}
        toggle={toggleProfileDropdown}
        className='ms-sm-3 header-item topbar-user'
      >
        <DropdownToggle tag='button' type='button' className='btn'>
          <span className='d-flex align-items-center'>
            <i className='ri-user-3-fill' style={{ fontSize: 26 }}></i>
            <span className='text-start ms-xl-2'>
              <span className='d-none d-xl-inline-block ms-1 fw-medium user-name-text'>
                {userName}
              </span>
              <span className='d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text'>
                Creator
              </span>
            </span>
          </span>
        </DropdownToggle>
        <DropdownMenu className='dropdown-menu-end'>
          <h6 className='dropdown-header'>
            Welcome {userName}!
          </h6>
          <div className='dropdown-item-text'>
            <small className='text-muted'>{userEmail}</small>
          </div>
          <DropdownItem className='p-0' disabled>
            <Link to='/profile' className='dropdown-item'>
              <i className='mdi mdi-account-circle text-muted fs-16 align-middle me-1'></i>
              <span className='align-middle'>Profile</span>
            </Link>
          </DropdownItem>
          <DropdownItem className='p-0' disabled>
            <Link to='' className='dropdown-item'>
              <i className='mdi mdi-dots-circle text-muted fs-16 align-middle me-1'></i>{' '}
              <span className='align-middle'>
                Circle : <b>Experience Engineering</b>
              </span>
            </Link>
          </DropdownItem>
          <DropdownItem className='p-0' disabled>
            <Link to='' className='dropdown-item'>
              <i className='mdi mdi-lightning-bolt text-muted fs-16 align-middle me-1'></i>{' '}
              <span className='align-middle'>
                Status : <b>Creator</b>
              </span>
            </Link>
          </DropdownItem>

          <div className='dropdown-divider'></div>
          <DropdownItem className='p-0' disabled>
            <Link to='' className='dropdown-item'>
              <i className='mdi mdi-wallet text-muted fs-16 align-middle me-1'></i>{' '}
              <span className='align-middle'>
                Balance : <b>Unlimited</b>
              </span>
            </Link>
          </DropdownItem>
          <DropdownItem className='p-0' disabled>
            <Link to='/pages-profile-settings' className='dropdown-item'>
              <span className='badge bg-success-subtle text-success mt-1 float-end'>
                New
              </span>
              <i className='mdi mdi-cog-outline text-muted fs-16 align-middle me-1'></i>{' '}
              <span className='align-middle'>Settings</span>
            </Link>
          </DropdownItem>
          <DropdownItem className='p-0' disabled>
            <Link to='/pages-faqs' className='dropdown-item'>
              <i className='mdi mdi-lifebuoy text-muted fs-16 align-middle me-1'></i>{' '}
              <span className='align-middle'>Help</span>
            </Link>
          </DropdownItem>
          <DropdownItem className='p-0'>
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className='dropdown-item'
              style={{
                border: 'none',
                background: 'none',
                width: '100%',
                textAlign: 'left',
                opacity: isLoggingOut ? 0.6 : 1,
                cursor: isLoggingOut ? 'not-allowed' : 'pointer'
              }}
            >
              {isLoggingOut ? (
                <>
                  <i className='mdi mdi-loading mdi-spin text-muted fs-16 align-middle me-1'></i>{' '}
                  <span className='align-middle'>
                    Logging out...
                  </span>
                </>
              ) : (
                <>
                  <i className='mdi mdi-logout text-muted fs-16 align-middle me-1'></i>{' '}
                  <span className='align-middle' data-key='t-logout'>
                    Logout
                  </span>
                </>
              )}
            </button>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </React.Fragment>
  )
}

export default ProfileDropdown
