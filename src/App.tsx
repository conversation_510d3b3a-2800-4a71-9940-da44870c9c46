import React, { useEffect } from "react";

//import Scss
import "./assets/scss/themes.scss";

//imoprt Route
import Route from "./Routes";

// User Context Provider
import { UserProvider } from "./context/UserContext";
import { AuthProvider } from "auth/AuthContext";
// import fakeBackend from './helpers/AuthType/fakeBackend'

// Activating fake backend
// fakeBackend()

function App() {
  useEffect(() => {
    document.documentElement.setAttribute("data-sidebar-size", "sm");
    // localStorage.clear()
  }, []);

  return (
    <React.Fragment>
      <AuthProvider>
        <UserProvider>
          <Route />
        </UserProvider>
      </AuthProvider>
    </React.Fragment>
  );
}

export default App;
