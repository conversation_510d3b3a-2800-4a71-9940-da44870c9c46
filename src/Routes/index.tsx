import React from "react";
import {
  Route,
  createRoutesFromElements,
  createBrowser<PERSON><PERSON>er,
  RouterProvider,
  Navigate
} from "react-router-dom";

//Layouts
import VerticalLayout from "../Layouts/index";
import NonAuthLayout from "Layouts/NonAuthLayout";

//routes
import { publicRoutes, protectedRoutes } from "./allRoutes";
import { EditableProvider } from "pages/ImageToCode/Contaxt";
import ProtectedRoute from "../component/auth/ProtectedRoute";

const Index = () => {
  const router = createBrowserRouter(
    createRoutesFromElements(
      <Route>
        {/* Public routes - no authentication required */}
        {publicRoutes.map((route, idx) => (
          <Route
            path={route.path}
            element={
              <NonAuthLayout>
                {route.component}
              </NonAuthLayout>
            }
            key={`public-${idx}`}
          />
        ))}

        {/* Protected routes - authentication required */}
        <Route element={<ProtectedRoute />}>
          {protectedRoutes.map((route, idx) => (
            <Route
              path={route.path}
              element={
                <NonAuthLayout>
                  <EditableProvider>
                    <VerticalLayout>{route.component}</VerticalLayout>
                  </EditableProvider>
                </NonAuthLayout>
              }
              key={`protected-${idx}`}
            />
          ))}
        </Route>

        {/* Fallback route - redirect unauthenticated users to login */}
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Route>
    ), {basename:"/experience/discover"}
  );

  return (
    <React.Fragment>
      <RouterProvider router={router} />
    </React.Fragment>
  );
};

export default Index;
