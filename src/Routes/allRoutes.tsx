import { Navigate } from "react-router-dom";

//Dashboard
import Home from "pages/Home";
import GenerateWireFrame from "pages/ProductDesign/GenerateWireFrame";

import ImageTOApp from "pages/ImageToCode";
import DesignAnalyser from "pages/DesignAnalyser/DesignAnalyser";
import DesignAnalysisResults from "pages/DesignAnalyser/results";
import LoginComponent from "component/auth/login";
import CallbackComponent from "component/auth/callback";
import LogoutCallbackComponent from "component/auth/logoutCallback";

// Public routes (no authentication required)
const publicRoutes = [
  // Authentication routes
  { path: "/login", component: <LoginComponent /> },
  { path: "/callback", component: <CallbackComponent /> },
  { path: "/logout", component: <LogoutCallbackComponent /> },
];

// Protected routes (authentication required)
const protectedRoutes = [
  { path: "/", component: <Home /> },
  { path: "/home", component: <Home /> },
  { path: "/image_to_app", component: <ImageTOApp /> },
  { path: "/generate_wireframe", component: <GenerateWireFrame />},
  { path: "/design_analyser", component: <DesignAnalyser /> },
  { path: "/design_analyser/results", component: <DesignAnalysisResults /> },
  // Fallback route for authenticated users
  { path: "*", component: <Navigate to="/" replace /> },
];

export { publicRoutes, protectedRoutes };