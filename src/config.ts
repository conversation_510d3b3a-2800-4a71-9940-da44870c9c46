// src/config.ts

interface Config {
  server: {
    MLO_SERVER: string;
  };
  auth: {
    API_AUTH_URL: string;
    APP_REDIRECT_URI: string;
  };
  api: {
    API_URL: string;
    CONTEXT_URL: string;
  };
  jira: {
    JIRA_SERVICE_ENDPOINT: string;
  };
  app: {
    BASE_PATH: string;
    LOGIN_PATH: string;
    CALLBACK_PATH: string;
    LOGOUT_PATH: string;
  };
}

// Declare runtime_config to avoid TypeScript errors
declare global {
  interface Window {
    runtime_config?: {
      MLO_SERVER?: string;
      API_AUTH_URL?: string;
      APP_REDIRECT_URI?: string;
      API_URL?: string;
      CONTEXT_URL?: string;
      JIRA_SERVICE_ENDPOINT?: string;
    };
  }
}

const config: Config = {
  server: {
    MLO_SERVER: window.runtime_config?.MLO_SERVER || process.env.REACT_APP_MLO_SERVER || "",
  },
  auth: {
    API_AUTH_URL: window.runtime_config?.API_AUTH_URL || process.env.REACT_APP_API_AUTH_URL || "",
    APP_REDIRECT_URI: window.runtime_config?.APP_REDIRECT_URI || process.env.REACT_APP_APP_REDIRECT_URI || `${window.location.origin}/experience/discover`,
  },
  api: {
    API_URL: window.runtime_config?.API_URL || process.env.REACT_APP_API_URL || "",
    CONTEXT_URL: window.runtime_config?.CONTEXT_URL || process.env.REACT_APP_CONTEXT_URL || "",
  },
  jira: {
    JIRA_SERVICE_ENDPOINT: window.runtime_config?.JIRA_SERVICE_ENDPOINT || process.env.REACT_APP_MLO_JIRA_SERVICE_ENDPOINT || "",
  },
  app: {
    BASE_PATH: "/experience/discover",
    LOGIN_PATH: "/login",
    CALLBACK_PATH: "/callback",
    LOGOUT_PATH: "/logout",
  }
};

// Utility functions for URL construction
export const getAuthUrls = () => ({
  loginUrl: `${window.location.origin}${config.app.BASE_PATH}${config.app.LOGIN_PATH}`,
  callbackUrl: `${window.location.origin}${config.app.BASE_PATH}${config.app.CALLBACK_PATH}`,
  logoutUrl: `${window.location.origin}${config.app.BASE_PATH}${config.app.LOGOUT_PATH}`,
  redirectUri: config.auth.APP_REDIRECT_URI,
});

// Utility function to get API endpoints
export const getApiEndpoints = () => ({
  auth: {
    login: `${config.auth.API_AUTH_URL}/auth/login-url`,
    token: `${config.auth.API_AUTH_URL}/auth/token`,
    refresh: `${config.auth.API_AUTH_URL}/auth/refresh-token`,
    logout: `${config.auth.API_AUTH_URL}/auth/logout-url`,
  },
  api: {
    base: config.api.API_URL,
    context: config.api.CONTEXT_URL,
  },
  server: {
    mlo: config.server.MLO_SERVER,
  },
  jira: {
    service: config.jira.JIRA_SERVICE_ENDPOINT,
  },
});

// Environment validation function
export const validateConfig = (): { isValid: boolean; missingVars: string[] } => {
  const requiredVars = [
    { key: 'auth.API_AUTH_URL', value: config.auth.API_AUTH_URL },
    { key: 'auth.APP_REDIRECT_URI', value: config.auth.APP_REDIRECT_URI },
    { key: 'server.MLO_SERVER', value: config.server.MLO_SERVER },
  ];

  const missingVars = requiredVars
    .filter(({ value }) => !value || value.trim() === '')
    .map(({ key }) => key);

  return {
    isValid: missingVars.length === 0,
    missingVars,
  };
};

export default config;