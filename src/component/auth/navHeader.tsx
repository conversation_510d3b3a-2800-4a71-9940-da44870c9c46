
import { useAuth } from "hooks/useAuth";
import React from "react";
import { Link } from "react-router-dom";

const NavHeader: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuth();

  return (
    <header
      style={{
        display: "flex",
        justifyContent: "space-between",
        padding: "1rem",
        background: "#eee",
      }}
    >
      <Link to="/">
        <h1>Experience Studio</h1>
      </Link>
      <nav>
        {isAuthenticated ? (
          <div>
            <span>Welcome, {user?.name || "User"}!</span>
            <button onClick={logout} style={{ marginLeft: "1rem" }}>
              Logout
            </button>
          </div>
        ) : (
          <Link to="/login">Login</Link>
        )}
      </nav>
    </header>
  );
};

export default NavHeader;
