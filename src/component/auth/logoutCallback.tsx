import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "hooks/useAuth";

const LogoutCallbackComponent: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    console.log('Logout callback received, redirecting to login immediately');

    // Clear any remaining authentication state
    if (isAuthenticated) {
      console.log('User still appears authenticated, clearing state...');
    }

    // Redirect to login immediately
    navigate("/login", { replace: true });
  }, [navigate, isAuthenticated]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div>Logout successful!</div>
      <div style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
        Redirecting to login...
      </div>
    </div>
  );
};

export default LogoutCallbackComponent;
