import { useAuth } from "hooks/useAuth";
import React, { useEffect, useState } from "react";

import { useNavigate } from "react-router-dom";

const LoginComponent: React.FC = () => {
  const { login, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const [isRedirectingToOAuth, setIsRedirectingToOAuth] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/", { replace: true });
      return;
    }

    // Check if this is an OAuth callback (has code parameter)
    const urlParams = new URLSearchParams(window.location.search);
    const hasOAuthParams = urlParams.get('code') || urlParams.get('refresh_token');

    // If this is an OAuth callback, let AuthContext handle it
    if (hasOAuthParams) {
      return;
    }

    // Automatically trigger login if not authenticated and not processing OAuth callback
    if (!isLoading && !isAuthenticated) {
      setIsRedirectingToOAuth(true);
      login();
    }
  }, [isAuthenticated, isLoading, login, navigate]);

  // Spinner component
  const Spinner = () => (
    <div style={{
      width: '40px',
      height: '40px',
      border: '4px solid #f3f3f3',
      borderTop: '4px solid #007bff',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginBottom: '20px'
    }}>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );

  const containerStyle = {
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: '#f8f9fa',
    color: '#333'
  };

  const messageStyle = {
    fontSize: '18px',
    fontWeight: '500',
    marginBottom: '10px'
  };

  const subMessageStyle = {
    fontSize: '14px',
    color: '#666',
    textAlign: 'center' as const,
    maxWidth: '400px'
  };

  if (isLoading) {
    return (
      <div style={containerStyle}>
        <Spinner />
        <div style={messageStyle}>Checking authentication...</div>
        <div style={subMessageStyle}>Please wait while we verify your login status</div>
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div style={containerStyle}>
        <Spinner />
        <div style={messageStyle}>Authentication successful!</div>
        <div style={subMessageStyle}>Redirecting to your dashboard...</div>
      </div>
    );
  }

  // Show loading while redirecting to OAuth
  return (
    <div style={containerStyle}>
      <Spinner />
      <div style={messageStyle}>Redirecting to login...</div>
      <div style={subMessageStyle}>
        {isRedirectingToOAuth
          ? "Please wait while we redirect you to the secure login page"
          : "Preparing your login experience..."
        }
      </div>
    </div>
  );
};

export default LoginComponent;
