import React, { useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useAuth } from "hooks/useAuth";
import config from "../../config";
import * as authService from "../../auth/authService";

const CallbackComponent: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { handleLoginSuccess } = useAuth();

  useEffect(() => {
    const code = searchParams.get("code");
    const refreshToken = searchParams.get("refresh_token");

    const handleAuth = async () => {
      try {
        if (code) {
          await authService.exchangeCodeForToken(
            code,
            config.auth.APP_REDIRECT_URI
          );
        } else if (refreshToken) {
          await authService.refreshToken(refreshToken);
        }
        handleLoginSuccess();
        navigate("/");
      } catch (error) {
        console.error("Authentication failed:", error);
        navigate("/login");
      }
    };

    handleAuth();
  }, [searchParams, navigate, handleLoginSuccess]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div>Processing authentication...</div>
      <div style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
        Please wait while we complete your login
      </div>
    </div>
  );
};

export default CallbackComponent;
