import React, { createContext, useContext, ReactNode, useEffect, useState } from 'react';
import { getAccessToken, getUsername, getName,  } from 'auth/tokenStorage';

interface UserAccount {
  username: string;
  name: string;
}

interface UserContextType {
  accounts: UserAccount[];
  instance: {
    getActiveAccount: () => UserAccount | null;
  };
  updateUserFromTokens: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

const fallbackUser: UserAccount = {
  username: '<EMAIL>',
  name: 'MLO User'
};

export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<UserAccount>(fallbackUser);
  const [accounts, setAccounts] = useState<UserAccount[]>([fallbackUser]);

  const updateUserFromTokens = () => {
    // Get user info from authentication tokens
    const authUsername = getUsername();
    const authName = getName();
    const accessToken = getAccessToken();

    console.log('UserContext: Updating user from tokens', {
      authUsername,
      authName,
      hasToken: !!accessToken
    });

    if (accessToken && authUsername && authUsername !== '<EMAIL>') {
      // We have valid authentication tokens with real user data
      const authenticatedUser = {
        username: authUsername,
        name: authName || 'MLO User'
      };
      setAccounts([authenticatedUser]);
      setCurrentUser(authenticatedUser);
      console.log('UserContext: Setting authenticated user', authenticatedUser);
    } else {
      // No valid tokens or still fallback user, keep fallback
      console.log('UserContext: No valid tokens or fallback user, keeping fallback');
      setAccounts([fallbackUser]);
      setCurrentUser(fallbackUser);
    }
  };

  useEffect(() => {
    // Initial check on mount
    updateUserFromTokens();

    // Listen for storage changes (when tokens are updated)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'access_token' || e.key === 'username' || e.key === 'name') {
        console.log('UserContext: Storage changed, updating user');
        updateUserFromTokens();
      }
    };

    // Listen for custom auth events
    const handleAuthChange = () => {
      console.log('UserContext: Auth state changed, updating user');
      setTimeout(updateUserFromTokens, 100); // Small delay to ensure tokens are set
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('authStateChanged', handleAuthChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authStateChanged', handleAuthChange);
    };
  }, []);

  const contextValue: UserContextType = {
    accounts,
    instance: {
      getActiveAccount: () => currentUser
    },
    updateUserFromTokens
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// For backward compatibility with MSAL hook name
export const useSSO = useUser;
