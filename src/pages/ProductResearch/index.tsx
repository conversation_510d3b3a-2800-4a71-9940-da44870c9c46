import { useUser } from "../../context/UserContext";
import { GET_BRAINSTORMER_ENDPOINT, MLO_SERVER } from "Mike/constants";
import { EmailPayload } from "Mike/models/playResponse";
import { axiosInstance } from "Mike/utils/axiosConfig";
import Content from "pages/ProductResearch/MyIdeas";
import { useEffect, useState } from "react";

interface Project {
    base: { name: string };
    roadmap: boolean;
    id: string;
}

const MyIdeas = () => {
    const { accounts } = useUser();
    const [projects, setProjects] = useState<Project[]>([]);

    useEffect(() => {
        const payload: EmailPayload = { userEmail: accounts[0].username };
        axiosInstance
            .post(MLO_SERVER + GET_BRAINSTORMER_ENDPOINT, payload)
            .then((response) => {
                if (response.status === 200) {
                    setProjects(response.data.brainstormerProjects || []);
                }
            });
    }, [accounts]);

    const handleDelete = async (id: string) => {

    };

    return (
        <div>
            <Content
                projects={projects}
                setProjects={setProjects}
                onDelete={handleDelete}
            />
        </div>
    );
};

export default MyIdeas;
