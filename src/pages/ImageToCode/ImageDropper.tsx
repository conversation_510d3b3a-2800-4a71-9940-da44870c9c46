import { useUser } from "../../context/UserContext";
import { axiosInstance } from "Mike/utils/axiosConfig";
import React, { useEffect, useState } from "react";
import Dropzone from "react-dropzone";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Button,
  Card,
  CardBody,
  Col,
  Container,
  Form,
  FormFeedback,
  FormGroup,
  Input,
  InputGroup,
  Label,
  Modal,
  ModalBody,
  ModalHeader,
  Row,
  Spinner
} from "reactstrap";
import { useEditable } from "./Contaxt";
import AlertComponent from "./Loading/AlertModal";

const ImageDropper: React.FC = () => {
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [imgModalOpen, setImgModalOpen] = useState<boolean>(false);

  // const [projects, setProjects] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [hover, setHover] = useState(false);
  const colorOptions = [
    "#a994b5",
    "#695d70",
    "#8c4db0",
    "#b8dbe0",
    "#5c4691",
    "#bac9db",
    "#9dcdd4",
    "#10b0c7",
    "#babcdb"
  ];

  const getInitialsColor = (index: any) => {
    return colorOptions[index % colorOptions.length];
  };

  const handleMouseEnter = () => setHover(true);
  const handleMouseLeave = () => setHover(false);
  const location = useLocation();
  let { state } = location;
  const {
    handleNextClickPreview,
    handleAcceptedFiles,
    deleteImage,
    selectedFiles,
    handleBoundingBoxEditor,
    setShowAppComponent,
    setApiResponse,
    setImageURI,
    loadingMessage,
    // previewAllprojectModalOpen,
    setLoadingMessage,
    // togglePrviewAllProjectModal,
    // setPreviewAllProjectModalOpen,
    // toggleAddProjectNameModal,
    // projectName,
    // handleAddProjectName,
    // setProjectName,
    // addProjectNameModalOpen,
    // setIsAlertModalOpen,
    // isAlertModalOpen,
    category,
    setCategory,
    setErrors,
    errors,
    // setCurr_ProjectId,
    // curr_projectId,
    // projectNameData,
    handleGenerateCode,
    setInputValue // Add this line to get the setInputValue function from the context
  } = useEditable();
  const navigate = useNavigate();

  const toggleImgModal = () => {
    setImgModalOpen(!imgModalOpen);
  };

  // const toggleModal = () => {
  //   setIsAlertModalOpen(!isAlertModalOpen);
  // };

  const toggleModalWithImage = (imagePreview: string | null) => {
    if (imagePreview) {
      setModalImage(imagePreview);
    }
    toggleImgModal();
  };

  const [loading, setLoading] = useState(false);
  const { accounts } = useUser();

  // useEffect(() => {
  //   if (previewAllprojectModalOpen) {
  //     setLoading(true);
  //     setLoadingMessage(
  //       "we will provide all the created projects shortly, please wait..."
  //     );
  //     axiosInstance
  //       .post("/image_to_code/get_image_to_code", { author: accounts[0].username })
  //       .then((response) => {
  //         console.log("Full response object:", response);

  //         const data = response.data;

  //         if (data) {
  //           const sortedProjects = data
  //             .map((item: any) => ({
  //               _id: item.id,
  //               name: item.projectName
  //             }))
  //             .sort((a: any, b: any) => a.name.localeCompare(b.name));
  //           console.log("sorted Projects:", sortedProjects);
  //           setProjects(sortedProjects);
  //         } else {
  //           console.error("No data found in response.");
  //         }
  //       })
  //       .catch((error) => {
  //         console.error("Error fetching projects:", error);
  //       })
  //       .finally(() => {
  //         setLoading(false);
  //         setLoadingMessage("");
  //       });
  //   }
  // }, [previewAllprojectModalOpen]);

  // handle Delete project
  // const handleDeleteProject = async (projectId: string) => {
  //   console.log(`Attempting to delete project with id: ${projectId}`);
  //   if (
  //     !projectId ||
  //     typeof projectId !== "string" ||
  //     projectId.trim() === ""
  //   ) {
  //     console.error("Invalid project ID:", projectId);
  //     return;
  //   }

  //   try {
  //     setLoading(true);
  //     setLoadingMessage("Deleting project, please wait...");
  //     const trimmedProjectId = projectId.trim();
  //     // setProjects((prevProjects) =>
  //     //   prevProjects.filter((project) => project._id !== projectId)
  //     // );
  //     const response = await axiosInstance.delete(
  //       `/image_to_code/delete_image_to_code?id=${trimmedProjectId}`
  //     );
  //     console.log(response);
  //     if (response && response.status === 200) {
  //       console.log(`Project with id ${projectId} deleted successfully`);
  //     } else {
  //       setProjects((prevProjects) =>
  //         prevProjects.concat({
  //           _id: projectId,
  //           projectName: "Project Name",
  //           description: "No description"
  //         })
  //       );
  //     }
  //   } catch (error: any) {
  //     console.error(`Error deleting project with id ${projectId}:`, error);
  //     setProjects((prevProjects) =>
  //       prevProjects.concat({
  //         _id: projectId,
  //         projectName: "Project Name",
  //         description: "No description"
  //       })
  //     );

  //     if (error.response) {
  //       console.error("Response data:", error.response.data);
  //       console.error("Response status:", error.response.status);
  //       console.error("Response headers:", error.response.headers);
  //     } else if (error.request) {
  //       console.error("No response received:", error.request);
  //     } else {
  //       console.error("Error message:", error.message);
  //     }
  //   } finally {
  //     setLoading(false);
  //     setLoadingMessage(""); // Clear the loading message
  //   }
  // };

  // Handle Get Project By Id
  // const handleGetProjectById = async (projectId: string) => {
  //   if (curr_projectId) {
  //     setCurr_ProjectId("");
  //     console.log("Clear Current project Id state", curr_projectId);
  //   }
  //   console.log("Project id:", projectId);
  //   const projectNameInLocal = localStorage.getItem("projectAdded");
  //   const projectHistoryInLocal = localStorage.getItem("project_Data_History");
  //   if (projectNameInLocal || projectHistoryInLocal) {
    
  //     localStorage.clear();
  //   }
  //   console.log(`Attempting to fetch project with id: ${projectId}`);
  //   if (
  //     !projectId ||
  //     typeof projectId !== "string" ||
  //     projectId.trim() === ""
  //   ) {
  //     console.error("Invalid project ID:", projectId);
  //     return;
  //   }

  //   try {
  //     setLoading(true);
  //     setLoadingMessage("Fetching project, please wait...");


  //     const response = await axiosInstance.get(
  //       `/image_to_code/get_image_to_code/${projectId}`
  //     );
  //     console.log("Project Data by Id:", response);
  //     if (response && response.status === 200) {
  //       console.log(`Project with id ${projectId} fetched successfully`);
  //       // Handle the fetched project data here
  //       const fetchedProject = response.data;
  //       // You might want to update the state or perform some action with the fetched project
  //       console.log("Fatched project b id: _", fetchedProject);
  //       setCurr_ProjectId(projectId);

  //       localStorage.setItem(
  //         "project_Data_History",
  //         JSON.stringify(fetchedProject)
  //       );

  //       setShowAppComponent("priviewIframe");
  //       setApiResponse(fetchedProject.code);
  //       setImageURI(fetchedProject.image[0]);
  //       console.log("ImageUri", fetchedProject.image[0]);
  //       togglePrviewAllProjectModal(); // Close the modal after fetching the project
  //     }
  //   } catch (error: any) {
  //     console.error(`Error fetching project with id ${projectId}:`, error);
  //     // Handle the error appropriately
  //   } finally {
  //     setLoading(false);
  //     setLoadingMessage("");
  //   }
  // };

  // useEffect(() => {
  //   if (state) {
  //     console.log("Setting code from design")
      
  //     //setProjectName(state.appName)
  //     const setupProjectData =async () =>{
  //       setApiResponse(state.code)
  //       setImageURI(state.design)
  //       // await handleAddProjectName(state.appName,"NEW_PRODUCT",state.code, state.design)
  //       console.log("sETTING THE CODE")
  //       setShowAppComponent("priviewIframe");
  //       navigate(location.pathname, { replace: true });

  //     } 
  //     setupProjectData()
  //   }
  // }, [ navigate, location.pathname, setApiResponse, setShowAppComponent, state, setImageURI]);

  const handleMoveToEditor = () => {
    handleBoundingBoxEditor();
  };

  const gridStyle = {
    display: "grid",
    gridTemplateColumns: "repeat(3, 1fr)",
    gap: "10px"
  };

  // const rearrangedProjects = [...projects];
  // const mostRecentProject = rearrangedProjects.pop();
  // rearrangedProjects.unshift(mostRecentProject);

  const handleGenerateClick = () => {
    // setInputValue("convert the given image to code");
    handleGenerateCode();
  };

  return (
    <React.Fragment>
      <div className="page-content">
        <Container fluid>
          <Row>
            <Card style={{ height: "57.5vh" }}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  padding: "10px"
                }}
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <div className="text-warning fs-1 rounded">
                    <i className="ri-information-line"></i>
                  </div>
                  <p className="text-muted mb-0" style={{ marginLeft: "10px" }}>
                    You can upload your pencil sketch or your Figma design
                  </p>
                </div>
                <div>
                  {/* <i
                    className="px-2 py-2 mx-2 ri-folder-add-line rounded-pill shadow-lg"
                    onClick={() => setPreviewAllProjectModalOpen(true)}
                    style={{ cursor: "pointer", fontSize: "20px" }}
                  ></i> */}
                  <i
                    className="px-2 py-2 ri-arrow-right-line rounded-pill shadow-lg back-btn"
                    onClick={handleNextClickPreview}
                    style={{ cursor: "pointer", fontSize: "20px" }}
                  ></i>
                </div>
              </div>
              <CardBody>
                <Dropzone onDrop={handleAcceptedFiles}>
                  {({ getRootProps, getInputProps }) => (
                    <div className="dropzone dz-clickable" {...getRootProps()}>
                      <input {...getInputProps()} />
                      <div className="dz-message needsclick">
                        <div className="mb-3">
                          <i className="display-4 text-muted ri-upload-cloud-2-fill" />
                        </div>
                        <h4>Drop files here or click to upload.</h4>
                      </div>
                    </div>
                  )}
                </Dropzone>
                <div className="list-unstyled mb-0" id="file-previews">
                  {selectedFiles.map((f: any, index: number) => (
                    <Card
                      className="mt-1 mb-3 shadow-none border dz-image-preview"
                      key={index + "-file"}
                    >
                      <div className="p-2">
                        <Row className="align-items-center">
                          <Col className="col-auto">
                            <img
                              data-dz-thumbnail=""
                              height="80"
                              className="avatar-sm rounded bg-light"
                              alt={f.name}
                              src={f.preview}
                              onClick={() => toggleModalWithImage(f.preview)}
                              style={{ cursor: "pointer" }}
                            />
                          </Col>
                          <Col
                            onClick={() => toggleModalWithImage(f.preview)}
                            style={{ cursor: "pointer" }}
                          >
                            <Link
                              to="#"
                              className="text-muted font-weight-bold"
                            >
                              {f.name}
                            </Link>
                            <p className="mb-0">
                              <strong>{f.formattedSize}</strong>
                            </p>
                          </Col>
                          <Col className="ml-auto">
                            <button
                              className="btn btn-info btn-sm mt-2 mx-3"
                              onClick={handleMoveToEditor}
                            >
                              Edit
                            </button>
                            <button
                              className="btn btn-danger btn-sm mt-2"
                              onClick={() => deleteImage(index)}
                            >
                              Delete
                            </button>
                          </Col>
                        </Row>
                      </div>
                    </Card>
                  ))}
                </div>
                <Button
                  className="generate-button"
                  onClick={handleGenerateClick}
                  disabled={selectedFiles.length === 0} // Disable button if no files are selected
                >
                  Generate
                </Button>
              </CardBody>
            </Card>
          </Row>
        </Container>
      </div>

      {/* Modal for Image Preview */}
      <Modal isOpen={imgModalOpen} toggle={toggleImgModal} size="lg">
        <ModalHeader toggle={toggleImgModal as any}>Image Preview</ModalHeader>
        <ModalBody>
          {modalImage && (
            <img
              src={modalImage}
              alt="Preview"
              style={{ maxWidth: "100%", maxHeight: "100%" }}
            />
          )}
        </ModalBody>
      </Modal>

      {/* Preview All Project list Modal */}


      {/* Add Project NameModal */}
      {/* <Modal isOpen={addProjectNameModalOpen} size="md" centered>
        <ModalHeader
          toggle={toggleAddProjectNameModal}
          style={{
            backgroundColor: "#8c4db0",
            display: "flex",
            padding: "10px",
            height: "40px",
            position: "relative"
          }}
          close={
            <button
              style={{
                position: "absolute",
                right: "20px",
                top: "50%",
                transform: "translateY(-50%)",
                background: "transparent",
                border: "none",
                color: "white",
                fontSize: "30px",
                cursor: "pointer"
              }}
              onClick={toggleAddProjectNameModal}
            >
              &times;
            </button>
          }
        >
          <h5 style={{ color: "white", margin: "auto" }}>Add Project</h5>
        </ModalHeader>
        <ModalBody>
          {loading ? (
            <div className="text-center my-4">
              <Spinner color="primary" />
              <p>{loadingMessage}</p>
            </div>
          ) : (
            <Form>
              <FormGroup>
                <Label for="projectName">Project Name</Label>
                <Input
                  type="text"
                  name="projectName"
                  id="projectName"
                  placeholder="Enter project name"
                  value={projectName}
                  // onChange={(e) => setProjectName(e.target.value)}
                  onChange={(e) => {
                    setProjectName(e.target.value);
                    if (errors.projectName)
                      setErrors((prev) => ({ ...prev, projectName: "" }));
                  }}
                  invalid={!!errors.projectName}
                />
                <FormFeedback>{errors.projectName}</FormFeedback>
              </FormGroup>
              <FormGroup>
                <label htmlFor="vertical" className="form-label">
                  Select the strategy
                </label>
                <Input
                  type="select"
                  required={true}
                  className="form-select"
                  aria-label="Default select example"
                  value={category}
                  // onChange={(e) => setCategory(e.target.value)}
                  onChange={(e) => {
                    setCategory(e.target.value);
                    if (errors.category)
                      setErrors((prev) => ({ ...prev, category: "" }));
                  }}
                  invalid={!!errors.category}
                >
                  <option>Select product strategy</option>
                  <option value="NEW_PRODUCT">New Product</option>
                  <option value="LEGACY_MODERNIZATION">
                    Legacy Modernization
                  </option>
                </Input>
                <FormFeedback>{errors.category}</FormFeedback>
              </FormGroup>
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button color="primary" onClick={() => { handleAddProjectName(projectName, category) }}>
                  Add Project
                </Button>
              </div>
            </Form>
          )}
        </ModalBody>
      </Modal> */}

      {/* <AlertComponent
        isAlertModalOpen={isAlertModalOpen}
        toggleModal={toggleModal}
        setPreviewAllProjectModalOpen={setPreviewAllProjectModalOpen}
        title="Project Name Missing"
        desc="The project name or project history is missing. Please add the project name to proceed."
      /> */}
    </React.Fragment>
  );
};

export default ImageDropper;
