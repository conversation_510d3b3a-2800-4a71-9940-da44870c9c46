import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "reactstrap";
import { useEditable } from "./Contaxt";
import APIError from "./Error/API-Error-page";
import ImageToCodeContainer from "./ImageToCodeContainer";
import ChatBot from "./chatwindow"; // Import the ChatBot component
import "./input-field.css";
import { TbMessageChatbot } from "react-icons/tb";
import { motion } from "framer-motion"; // Import framer-motion

const ImageTOApp = () => {
  const {
    inputValue,
    handleChange,
    textareaRef,
    loading,
    showAppComponent,
    error,
    setIsSendButtonDisabled,
    handleGenerateCode,
    handleGenerateCodeForEditedImage
  } = useEditable();

  const [showChatInput, setShowChatInput] = useState(false);

  const handleKeyDown = (event : React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (
        showAppComponent === "ImageDropper" ||
        showAppComponent === "priviewIframe"
      ) {
        handleGenerateCode();
      } else if (showAppComponent === "AppDraw") {
        handleGenerateCodeForEditedImage();
      }
    }
  };

  useEffect(() => {
    setIsSendButtonDisabled(!inputValue);
  }, [inputValue, setIsSendButtonDisabled]);

  const renderInputCard = () => (
    <Card className="container-fluid input-card">
      {/* <CardBody>
        <div className="d-flex justify-content-end align-items-center chat-input-container">
          <textarea
            ref={textareaRef}
            className="chat-input chat-input bg-light border-light"
            value={inputValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your message here..."
          />
          <Button
            className={`send-button ${inputValue ? "enabled" : ""}`}
            disabled={!inputValue}
            onClick={() => {
              if (
                showAppComponent === "ImageDropper" ||
                showAppComponent === "priviewIframe"
              ) {
                handleGenerateCode();
              } else if (showAppComponent === "AppDraw") {
                handleGenerateCodeForEditedImage();
              }
            }}
          >
            <i className="text-white ri-send-plane-2-fill"></i>
          </Button>
        </div>
      </CardBody> */}
    </Card>
  );

  return (
    <React.Fragment>
      {error ? (
        <APIError />
      ) : (
        <>
          <ImageToCodeContainer />
          <Button
            className="chat-toggle-button-bot"
            onClick={() => setShowChatInput(!showChatInput)}
          >
            <TbMessageChatbot size={24} /> {/* Use the imported chat icon */}
          </Button>
          {showChatInput && (
            <motion.div
              initial={{ x: 300, opacity: 0 }} // Start off-screen to the right
              animate={{ x: 0, opacity: 1 }} // Slide into view
              exit={{ x: 300, opacity: 0 }} // Slide out of view
              transition={{ type: "spring", stiffness: 260, damping: 20 }}
            >
              <ChatBot /> {/* Render the ChatBot component */}
            </motion.div>
          )}
          {!loading &&
            (showAppComponent === "ImageDropper" ||
              showAppComponent === "priviewIframe" ||
              showAppComponent === "AppDraw") &&
            renderInputCard()}
        </>
      )}
    </React.Fragment>
  );
};

export default ImageTOApp;
