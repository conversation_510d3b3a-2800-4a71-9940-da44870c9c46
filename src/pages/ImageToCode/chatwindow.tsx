import React, { useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Spinner } from "reactstrap";
import SimpleBar from "simplebar-react";
import mlo from "../../assets/images/mikeLogo.png";
import userDummayImage from "../../assets/images/users/avatar-1.jpg";
import "./input-field.css";
import { useEditable } from "./Contaxt";

const ChatBot = () => {
    const {
        inputValue,
        handleChange,
        textareaRef,
        loading,
        showAppComponent,
        error,
        setIsSendButtonDisabled,
        handleGenerateCode,
        handleGenerateCodeForEditedImage,
        setInputValue // Add this to update the inputValue state
    } = useEditable();
    const userChatShow = useRef<HTMLDivElement | null>(null);
    const [curMessage, setcurMessage] = useState<string>("");
    const [Chat_Box_Username] = useState<any>("Michelangelo");
    const [Chat_Box_Image] = useState<any>(mlo);
    const [chatMessages, setChatMessages] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    };

    const sendMessage = async () => {
        if (inputValue.trim() === "") return;

        // Add user's message to the chat
        const newMessage = {
            msg: inputValue,
            datetime: new Date().toLocaleString(),
            sender: "user",
        };
        setChatMessages([...chatMessages, newMessage]);

        // Clear input field
        setInputValue(""); // Clear the inputValue state
        if (textareaRef.current) {
            textareaRef.current.value = ""; // Clear the input field in the UI
        }

        // Simulate loading
        setIsLoading(true);

        try {
            // Generate bot response
            {
                await handleGenerateCode();
                
            // } else if (showAppComponent === "AppDraw") {
            //     await handleGenerateCodeForEditedImage();
            // }
            }
            // Add bot response message immediately after API call
            const botResponseMessage = {
                msg: "Your response is created. Check the result.",
                datetime: new Date().toLocaleString(),
                sender: "bot",
            };
            setChatMessages((prevMessages) => [...prevMessages, botResponseMessage]);
        } catch (error) {
            console.error("Error generating code:", error);
        } finally {
            setIsLoading(false); // Stop loading spinner
        }
    };

    const renderInputCard = () => (
        <div className="chat-input-section-bot">
            <div className="chat-input-container-bot">
                <textarea
                    ref={textareaRef}
                    className="chat-input-bot"
                    value={inputValue}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    placeholder="Type your message here..."
                />
                <Button
                    className={`send-button-bot ${inputValue ? "enabled" : ""}`}
                    disabled={!inputValue || isLoading}
                    onClick={sendMessage}
                >
                    <i className="text-white ri-send-plane-2-fill"></i>
                </Button>
            </div>
        </div>
    );

    return (
        <div className="user-chat-bot" ref={userChatShow}>
            {/* Fixed Header */}
            <div className="chat-header-bot">
                <div className="p-3 user-chat-bot-topbar">
                    <Row className="align-items-center">
                        <Col>
                            <div className="d-flex align-items-center">
                                <div className="flex-shrink-0 chat-user-img online user-own-img align-self-center me-3 ms-0">
                                    <img
                                        src={Chat_Box_Image || userDummayImage}
                                        className="rounded-circle avatar-xs-bot"
                                        alt=""
                                    />
                                    <span className="user-status-bot"></span>
                                </div>
                                <div className="flex-grow-1 overflow-hidden">
                                    <h5 className="text-truncate mb-0 fs-16">
                                        <a
                                            className="text-reset username"
                                            href="#userProfileCanvasExample"
                                        >
                                            {Chat_Box_Username}
                                        </a>
                                    </h5>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>

            {/* Scrollable Chat Area */}
            <div className="chat-content">
                <div className="chat-messages-container-bot">
                    <SimpleBar
                        style={{
                            height: "calc(100vh - 250px)",
                            marginBottom: "20px",
                        }}
                    >
                        <ul
                            className="list-unstyled chat-conversation-list my-5"
                            id="users-conversation"
                        >
                            {chatMessages.map((message, index) => (
                                <li
                                    key={index}
                                    className={`chat-list-bot ${
                                        message.sender === "user" ? "right" : "left"
                                    }`}
                                >
                                    <div className="conversation-list">
                                        <div
                                            className={`user-chat-content-bot ${
                                                message.sender === "user" ? "right" : "left"
                                            }`}
                                        >
                                            <div className="ctext-wrap">
                                                <div className="ctext-wrap-content chat-bubble">
                                                    <p>{message.msg}</p>
                                                </div>
                                            </div>
                                            <div className="conversation-name-bot">
                                                <small className="text-muted time">
                                                    {message.datetime}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                            {/* Loading spinner */}
                            {isLoading && (
                                <li className="chat-list-bot left">
                                    <div className="conversation-list">
                                        <div className="user-chat-content-bot left">
                                            <div className="ctext-wrap">
                                                <div className="ctext-wrap-content chat-bubble">
                                                    <Spinner size="sm" className="me-2" />
                                                    <span>Generating response...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            )}
                        </ul>
                    </SimpleBar>
                </div>
            </div>

            {/* Fixed Input Section */}
            {renderInputCard()}
        </div>
    );
};
// export { sendMessage };
export default ChatBot;
