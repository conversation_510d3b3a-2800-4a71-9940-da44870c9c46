.input-card-bot {
  position: relative;
  bottom: 0;
  width: 100%;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: none;
}
.user-chat-bot {
  position: fixed;
  bottom: 5vh; /* Dynamically adjusts relative to viewport height */
  right: 2vw; /* Dynamically adjusts relative to viewport width */
  width: calc(70vh + 5vw); /* Responsive width based on viewport height and width */
  max-width: 450px; /* Maximum width for larger screens */
  height: 70vh; /* Adjust height relative to viewport */
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-radius: 10px;
  overflow: hidden;
}
.user-chat-bot:hover {
  transform: scale(1.02); /* Slight scaling on hover */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
/* Fixed Header Styles */
.chat-header-bot {
  position: sticky;
  top: 0;
  background-color: #ffffff;
  border-bottom: 2px solid #e0e0e0; /* Slightly thicker border for a distinct look */
  z-index: 1001;
  flex-shrink: 0;
  padding: 10px; /* Increased padding for better readability */
  font-size: 1rem; /* Consistent font size */
  font-weight: bold; /* Improved visibility */
}

/* .chat-input:focus {
  background-color: #fff;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
} */

/* .chat-input::placeholder {
  color: #999;
}
   */

/* Scrollable Chat Area Styles */
.chat-messages-container-bot {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f5f5f5;
  position: relative;
  scrollbar-width: none; /* Firefox */
  height: calc(100% - 140px); /* Adjust based on header and footer height */
}

/* Fixed Input Section Styles */
.chat-input-section-bot {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  padding: 10px;
  z-index: 1001;
}

.chat-toggle-button-bot {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background-color: #ffffff;
  border: 2px solid #e0e0e0; /* Added border */
  border-radius: 50%;
  padding: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.chat-toggle-button-bot:hover {
  transform: scale(1.1);
  background-color: #ffffff;
}

.chat-input-container-bot {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  padding: 5px;
  border-radius: 25px;
  background-color: #f5f5f5;
  border: 2px solid #e0e0e0;
}

.chat-input-bot {
  flex: 1;
  padding: 10px 15px;
  border: none;
  border-radius: 20px;
  background-color: transparent;
  resize: none;
  max-height: 100px;
  font-size: 14px;
  outline: none;
}

.send-button-bot {
  min-width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007bff;
  border: 2px solid transparent;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
}

.send-button-bot:disabled {
  opacity: 0.5;
  background-color: #0056b3;
  
  cursor: not-allowed;
}

.send-button-bot.enabled:hover {
  background-color: #0056b3;
  transform: scale(1.1);
}

/* Message Styles */
.chat-list-bot {
  margin-bottom: 20px;
  clear: both;
}

.user-chat-content-bot {
  display: inline-block;
  max-width: 80%;
  padding: 10px;
  border-radius: 15px;
  margin-bottom: 5px;
  word-wrap: break-word; /* Ensure long words break */
}

.user-chat-content-bot.right {
  background-color: #dcf8c6;
  color: black;
  float: right;
  margin-left: auto;
}

.user-chat-content-bot.left {
  background-color: #e0f7fa; /* Light green background for user messages */
  color: black;
  float: left;
  margin-right: auto;
}
.conversation-name-bot {
  font-size: 12px;
  margin-top: 5px;
  clear: both;
}

/* .chat-bubble {
  background-color: white; /* Change background color to white */
  /* border: 1px solid black; Add a black border */
  /* border-radius: 10px; Optional: Add border radius for rounded corners */
  /* padding: 10px; Optional: Add padding for better spacing */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */

/* } */ 

/* Utilities */
.avatar-xs-bot {
  width: 32px;
  height: 32px;
}

.user-status-bot {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: #28a745;
  border-radius: 50%;
  border: 2px solid white;
}

/* .send-button {
    width: 45px;
    height: 45px;
    border-radius: 50px;
    color: white;
    margin-left: 10px;
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    display: flex;
    align-items: center;
    justify-content: center;
  } */

  /* .send-button.enabled {
    background-color: #007bff;
    border-color: #007bff;
    cursor: pointer;
  } */

  /* .send-button i {
    font-size: 18px;
  } */

@media (max-width: 768px) {
  .user-chat-bot {
    width: 95%;
    height: 60vh;
    bottom: 4vh;
    right: 2vw;
  }

  .chat-messages-container-bot {
    padding: 5px;
  }

  .chat-header-bot {
    font-size: 0.9rem;
  }

  .chat-input-bot {
    font-size: 12px;
  }

  .send-button-bot {
    min-width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .user-chat-bot {
    width: 100%;
    bottom: 0;
    right: 0;
    height: 50vh;
    border-radius: 0;
  }
  .chat-header-bot {
    font-size: 0.85rem;
    padding: 8px;
  }

  /* .chat-input-container {
    padding: 10px;
    border-radius: 50px;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    border: 1px solid #ccc;
  } */

  .chat-input-container-bot {
    gap: 5px;
  }

  .chat-input-bot {
    font-size: 12px;
    padding: 6px 10px;
  }

  .send-button-bot {
    min-width: 30px;
    height: 30px;
  }
}


.generate-button {
  display: block;
  margin: 20px auto; /* Center the button horizontally */
  padding: 10px 20px;
  font-size: 16px;
  background-color: #ffffff;
  color: black;
  border: 2px solid  lightgrey;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* .generate-button:hover {
  background-color: lightgrey;
} */

.generate-button:focus {
  outline: none;
}