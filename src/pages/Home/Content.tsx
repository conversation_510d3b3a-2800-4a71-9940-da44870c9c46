import React, { useEffect } from "react";
import { Card, Col, Row } from "reactstrap";
import { useNavigate } from "react-router-dom";

// Images
import image07 from "../../../src/assets/images/products/image7.png";
import image7 from "../../../src/assets/images/products/generate.jpeg";
import img14 from "../../../src/assets/images/products/img1.jpeg";
import img15 from "../../../src/assets/images/products/img2.jpeg";
import img10 from "../../../src/assets/images/products/img3.jpeg";
import img11 from "../../../src/assets/images/products/img4.jpeg";
import img6 from "../../../src/assets/images/products/img7.jpeg";
import image14 from "../../../src/assets/images/products/website.jpeg";
import iaf from "../../../src/assets/images/products/iaf.jpeg";

// JSON data
const newIdeas = [
  {
    status: "New",
    image: image14,
    title: "Generate UI design",
    nav: "/generate_wireframe",
    link: undefined,
  },
  {
    status: "New",
    image: image7,
    title: "Image to App",
    nav: "/image_to_app",
    link: undefined,
  },
  {
    status: "New",
    image: image07,
    title: "Design Analysis",
    nav: "/design_analyser",
    link: undefined,
  },
  {
    status: "New",
    image: iaf,
    title: "Intelligent Accessibility Framework",
    nav: undefined,
    link: "https://beta.avateam.io/iaf/",
  },
];

interface HomeProps {
  heading: string;
  textFieldPlaceholder: string;
  generateButtonText: string;
}

const Content: React.FC<HomeProps> = ({
  heading,
  textFieldPlaceholder,
  generateButtonText,
}) => {
  document.title = "Ascendion AVA - MichelAngelo";

  const navigate = useNavigate();

  function handleCardClick(link?: string, href?: string) {
    if (link) {
      window.open(link, "_blank");
    } else if (href) {
      navigate(href);
    }
  }

  useEffect(() => {
    document.documentElement.setAttribute("data-sidebar-size", "sm");
  }, []);

  return (
    <React.Fragment>
      <div className="page-content">
        <div className="container-fluid">
          <div className="cards-container-wrapper">
            <div className="cards-container">
              <h5 className="head" style={{ textAlign: "left" }}>
                Let's craft something new today!
              </h5>
              <Row>
                {newIdeas.map((card, index) => (
                  <Col sm={2} xl={2} key={index} style={{ minWidth: "200px" }}>
                    <Card
                      className="ribbon-box explore-box card-animate"
                      onClick={() => handleCardClick(card.link, card.nav)}
                      style={{ cursor: card.link || card.nav ? "pointer" : "default" }}
                    >
                      <div className={`ribbon ribbon-primary ribbon-shape`}>
                        {card.status}
                      </div>
                      <img
                        className="card-img-top img-fluid"
                        src={card.image}
                        alt="Card cap"
                      />
                      <div className="card-body">
                        <h5 className="card-title">{card.title}</h5>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default Content;
