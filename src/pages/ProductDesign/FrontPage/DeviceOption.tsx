import React from "react";

const DeviceOption = ({
  icon,
  label,
  value,
  selected,
  onClick,
  disabled,
}: {
  icon: React.ReactNode;
  label: string;
  value: string;
  selected: boolean;
  onClick: (value: string) => void;
  disabled?: boolean;
}) => (
  <button
    onClick={() => !disabled && onClick(value)}
    style={{
      ...styles.button,
      ...(selected ? styles.selected : styles.unselected),
      ...(disabled ? styles.disabled : {}),
    }}
    disabled={disabled}
  >
    <span style={styles.icon}>{icon}</span>
    <span style={styles.label}>{label}</span>
  </button>
);

const styles = {
  button: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    borderRadius: '8px',
    border: '1px solid #ccc',
    cursor: 'pointer',
    transition: 'background-color 0.3s ease'
  },
  selected: {
    border: '1px solid #007bff',
    backgroundColor: '#e7f0fd',
    color: '#007bff'
  },
  unselected: {
    border: '1px solid #ccc',
    backgroundColor: 'white',
    hover: {
      backgroundColor: '#f9f9f9'
    }
  },
  disabled: {
    cursor: 'not-allowed',
    backgroundColor: '#f0f0f0',
    borderColor: '#ccc',
    color: '#999',
    ':hover': {
      backgroundColor: '#f0f0f0'
    }
  },
  icon: {
    fontSize: '24px'
  },
  label: {
    fontSize: '16px',
    fontWeight: '500'
  }
};

export default DeviceOption;