import React, { useState } from "react";
import FirstStep from "./FirstStep";
import LoadingStep from "./LoadingStep";

const ProjectWizard = ({ handleGenerate }) => {
  const [loading, setLoading] = useState(false);
  const [stepOneData, setStepOneData] = useState("");

  const handleGenerateProject = (data) => {
    setLoading(true);
    handleGenerate(data);
  };

  return (
    <div className="ttttt" style={{justifyContent: 'center', 
      alignItems: 'center', 
      display: 'flex',
      overflowY: 'auto',
      minHeight: '100vh',
      width: '100%',
      position: 'relative',
      top: '46.5px',
      left: 0,
      right: 0,
      bottom: 0}}>
      <div 
        style={styles.card
          
          
        }
      >
        {loading ? (
          <LoadingStep />
        ) : (
          <FirstStep onGenerate={handleGenerateProject} />
        )}
      </div>
    </div>
  );
};

const styles = {
  card: {
    backgroundColor: "white",
          borderRadius: "8px",
          boxShadow: "0px -8px 16px rgba(0, 0, 0, 0.2), 0px 8px 16px rgba(0, 0, 0, 0.2)", 
          padding: "60px",
          maxWidth: "510px",
          marginTop: "100px",
          marginBottom: "100px", 
          justifyContent: "center",
          alignItems:"center", 
          display:"flex",
          overflowY: "auto"
   }
};
export default ProjectWizard;