import React from 'react';

const Button = ({ text, onClick, isSelected, disabled }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      style={{
        padding: '70px 140px', // Increased padding for larger buttons
        fontSize: '36px', // Larger text size for better visibility
        borderRadius: '20px', // Larger border radius for rounded corners
        border: 'none',
        cursor: disabled ? 'not-allowed' : 'pointer', // Change cursor when disabled
        transition: 'transform 0.2s, box-shadow 0.3s, background-color 0.3s',
        margin: '30px', // Increased margin for more spacing
        background: isSelected
          ? 'linear-gradient(45deg, #e0d8f9, #f8f4ff)' // Light purple mixed with white
          : 'linear-gradient(45deg, #cceeff, #e6f7ff)', // Lighter blue gradient
        color: '#333', // Darker text color for contrast
        boxShadow: isSelected
          ? '0 0 10px 4px rgba(224, 216, 249, 0.7), 0 0 20px 10px #e0d8f9' // Light purple glow effect
          : '0 4px 8px rgba(0, 0, 0, 0.1)', // Regular shadow for non-selected
        transform: isSelected ? 'scale(1.1)' : 'scale(1)', // Slight zoom effect on selection
        opacity: disabled ? 0.6 : 1, // Reduced opacity when disabled
      }}
    >
      {text}
    </button>
  );
};

export default Button;
