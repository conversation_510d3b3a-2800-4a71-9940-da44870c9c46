import React from "react";

const DescriptionInput = ({ value, onChange, placeholder } : {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  // maxLength: number;
  
})  => (

  <div style={{ position: "relative" }}>
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      // maxLength={maxLength}
      style={{
        width: "100%",
        padding: "12px",
        borderRadius: "10px",
        border: "1px solid #ddd",
        fontSize: "14px",
        resize: "none",
        height: "100px",
        boxSizing: "border-box"
      }}
    />
    <div
      style={{
        position: "absolute",
        bottom: "8px",
        right: "12px",
        fontSize: "12px",
        color: "#aaa"
      }}
    >
      {/* {value.length}/{maxLength} */}
    </div>
  </div>
);

export default DescriptionInput;
