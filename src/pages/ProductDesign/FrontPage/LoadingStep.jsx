/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";
const LoadingStep = () => {
  const [progress, setProgress] = useState(0);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [mloFacts, setMloFacts] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const messages = [
    "Generating your design...",
    "Finding the right components...",
    "Have a cup of coffee while we finalize the design...",
    "Finalizing..."
  ];

  const mloFact = [
    "Ascendion's AVA+ MichelAngelo is an AI-powered tool that transforms simple sketches into fully functional, live websites.",

    "This tool is part of Ascendion's broader AVA+ platform, which integrates various AI-driven studios to enhance different aspects of software engineering.",

    "Instant Website Creation: <PERSON><PERSON>nge<PERSON> can transform a simple pencil sketch into a fully functional website within hours, significantly reducing the traditional development timeline.",

    "Global AI Innovation Hubs: Ascendion has established AI studios in Chennai, India, and Austin, Texas. The Chennai studio serves as a global hub for generative AI advancements.",

    "Client Success and Innovation: Ascendion's focus on generative AI has led to significant strides in innovation and client success, positioning the company as a leader in the GenAI landscape.",
    "Ascendion's AVA+ MichelAngelo is an AI-powered tool that transforms simple sketches into fully functional, live websites.",

    "This tool is part of Ascendion's broader AVA+ platform, which integrates various AI-driven studios to enhance different aspects of software engineering.",

    "Instant Website Creation: MichelAngelo can transform a simple pencil sketch into a fully functional website within hours, significantly reducing the traditional development timeline.",

    "Global AI Innovation Hubs: Ascendion has established AI studios in Chennai, India, and Austin, Texas. The Chennai studio serves as a global hub for generative AI advancements.",

    "Client Success and Innovation: Ascendion's focus on generative AI has led to significant strides in innovation and client success, positioning the company as a leader in the GenAI landscape."
  ];
  useEffect(() => {
    let interval;
    if (isLoading) {
      const totalDuration = 120 * 1000; // 60 seconds
      const intervalTime = totalDuration / 100; // Interval time for 1% progress

      interval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 1;
          if (newProgress >= 100) {
            clearInterval(interval);
            setIsLoading(false);
            return 100;
          }
          // Change message every 25%
          setCurrentMessageIndex(Math.floor(newProgress / 25));
          setMloFacts(Math.floor(newProgress / 15));
          return newProgress;
        });
      }, intervalTime);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isLoading]);

  return (
    <div className="loading-content">
      <h3>Michelangelo is generating your project.</h3>

      <p className="text-gray-600 mb-2">{messages[currentMessageIndex]}</p>
      <div className="progress-bar">
        <div className="progress-fill"></div>
      </div>

      <div className="relative w-64 h-1 bg-gray-200 rounded mx-auto mb-2">
        <div
          className="absolute top-0 left-0 h-full bg-violet-600 rounded transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
        <h3 className="text-sm text-gray-600 ">{progress}%</h3>
        <span>Please Wait...</span>
      </div>

      <div className="tip-card">
        <div className="tip-icon overflow- font-size-5">
          {" "}
          <h4> 💡 DID YOU KNOW?</h4>
        </div>

        <h6>{mloFact[mloFacts]}</h6>
        <div className="example">AVA+ Experience Studio : Powered by Ascendion </div>
      </div>

      <style jsx>{`
        .loading-content {
          text-align: center;
          padding: 40px 20px;
        }

        h3 {
          font-size: 20px;
          color: #333;
          margin-bottom: 8px;
        }

        .progress-bar {
          width: 200px;
          height: 4px;
          background: #e5e7eb;
          border-radius: 2px;
          margin: 30px auto;
          overflow: hidden;
        }

        .progress-fill {
          width: 30%;
          height: 100%;
          background: #7c3aed;
          animation: progress 2s infinite;
        }

        @keyframes progress {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(400%);
          }
        }

        .tip-card {
          background: #f9fafb;
          border-radius: 12px;
          padding: 15px;
          margin-top: 20px;
          text-align: left;
          overflow: auto;
        }

        .tip-icon {
          font-size: 24px;
          margin-bottom: 12px;
        }

        h4 {
          color: #6b7280;
          font-size: 12px;
          letter-spacing: 1px;
          margin-bottom: 12px;
        }

        .example {
          background: white;
          padding: 12px;
          border-radius: 8px;
          margin-top: 12px;
          color: #6b7280;
        }
      `}</style>
    </div>
  );
};

export default LoadingStep;
