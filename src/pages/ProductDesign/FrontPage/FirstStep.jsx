import { useState } from 'react';
import DeviceOption from './DeviceOption';
import DescriptionInput from './DescriptionInput';
import { FaSun, FaMoon } from 'react-icons/fa'; // Import icons from react-icons

const FirstStep = ({ onGenerate }) => {
  const [projectDescription, setProjectDescription] = useState('');
  const [selectedDevice, setSelectedDevice] = useState('desktop');
  const [selectedStyle, setSelectedStyle] = useState(null);
  const [error, setError] = useState(null);

  const devices = [
    { icon: '💻', label: 'Desktop', value: 'desktop' },
    { icon: '📱', label: 'Mobile', value: 'mobile' },
  ];

  const styleOptions = [
    { label: 'Light', icon: <FaSun /> },
    { label: 'Dark', icon: <FaMoon /> }
  ];

  const handleGenerate = () => {
    if (!projectDescription.trim()) {
      setError("Please provide a valid project prompt");
      return;
    }
    if (!selectedStyle) {
      setError('Please select a style for your project');
      return;
    }
    setError(null); // Clear error if validation passes

    const payload = {
      projectDescription,
      selectedDevice,
      selectedStyle
    };

    onGenerate(payload.projectDescription +".\nDevice : "+ payload.selectedDevice+".\n I want it in"+ payload.selectedStyle+" theme");
  };

  const handleDescriptionChange = (value) => {
    setProjectDescription(value);
    if (value.trim()) {
      setError(null); // Clear error when valid input is provided
    }
  };

  const handleStyleToggle = (style) => {
    setSelectedStyle(style === selectedStyle ? null : style);
    if (style !== selectedStyle) { //add this two lines 
      setError(null); 
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.space}>
          <div>
            <label style={styles.label}>
              Which device are you designing for?
            </label>
            <div style={styles.deviceContainer}>
              {devices.map((device) => (
                <DeviceOption
                  key={device.value}
                  {...device}
                  selected={selectedDevice === device.value}
                  onClick={setSelectedDevice}
                  disabled={device.value === 'mobile'} 
                />
              ))}
            </div>
          </div>

          <div>
            <label style={styles.label}>
              Describe your creation in plain English
            </label>
            <DescriptionInput
              value={projectDescription}
              onChange={handleDescriptionChange}
              placeholder="I want to create a website for insurance plans and enrollment"
              // maxLength={1000}
            />
           {error && error.includes('project prompt') && <p style={styles.error}>{error}</p>}
          </div>

          <div>
            <label style={styles.label}>
              Describe the preferred style for your project
            </label>
            <div style={styles.styleChips}>
              {styleOptions.map((style, index) => (
                <button
                  key={index}
                  style={{
                    ...styles.styleChip,
                    ...(selectedStyle === style.label ? styles.styleChipSelected : {})
                  }}
                  onClick={() => handleStyleToggle(style.label)}
                >
                  {style.icon} {style.label}
                </button>
              ))}
            </div>
            {error && error.includes('style') && <p style={styles.error}>{error}</p>}
          </div>

          <div style={styles.buttonContainer}>
            <button
              onClick={handleGenerate}
              style={{
                ...styles.generateButton,
                ...styles.buttonEnabled,
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#584cfc';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = '#6c63ff';
              }}
            >
              ✨Generate your design
              <span style={styles.sprinkle}></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  space: {
    display: "flex",
    flexDirection: "column",
    gap: "24px"
  },
  label: {
    display: "block",
    color: "#333",
    fontSize: "18px", // Slightly larger font size for better readability
    fontWeight: "500",
    marginBottom: "12px",
    // textAlign: "center"
  },
  deviceContainer: {
    display: 'flex',
    justifyContent: 'center',
    gap: '8px',
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    padding: "12px 24px",
    borderRadius: "8px",
    fontSize: "16px",
    cursor: "pointer",
    border: "none",
    transition: "background-color 0.3s ease"
  },
  buttonEnabled: {
    backgroundColor: '#007bff',
    color: 'white',
  },
  buttonDisabled: {
    backgroundColor: '#ffffff',
    cursor: 'not-allowed',
  },
  error: {
    color: 'red',
    fontSize: '14px',
    marginTop: '8px',
  },
  styleChips: {
    display: 'flex',
    gap: '40px', // Add spacing between the buttons
    margin: '8px 0',
    justifyContent: 'center', // Center the buttons horizontally
  },
  styleChip: {
    background: 'white',
    border: '1px solid #E5E7EB',
    borderRadius: '20px',
    padding: '8px 16px',
    fontSize: '14px',
    color: '#374151',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px', // Add spacing between the icon and the label
  },
  styleChipSelected: {
    background: '#3B82F6',
    borderColor: '#3B82F6',
    color: 'white',
  },
  generateButton: {
    position: 'relative',
    background: '#6c63ff',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    padding: '10px 20px',
    fontSize: '14px',
    cursor: 'pointer',
    overflow: 'hidden',
    transition: 'background 0.3s',
  },
  sprinkle: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: '150%',
    height: '150%',
    background: 'radial-gradient(circle, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 70%)',
    transform: 'translate(-50%, -50%)',
    opacity: 0,
    transition: 'opacity 0.3s',
  },
};

export default FirstStep;
