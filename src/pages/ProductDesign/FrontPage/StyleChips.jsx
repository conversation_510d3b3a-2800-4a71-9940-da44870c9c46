import React from 'react';

const StyleChips = ({ label, selected, onClick }) => (
  <button
    onClick={() => onClick(label)}
    style={{
      padding: '8px 16px',
      borderRadius: '20px',
      fontSize: '12px',
      backgroundColor: selected ? '#e7f0fd' : '#f9f9f9',
      color: selected ? '#007bff' : '#555',
      border: '1px solid #ddd',
      cursor: 'pointer',
    }}
  >
    {label}
  </button>
);

export default StyleChips;