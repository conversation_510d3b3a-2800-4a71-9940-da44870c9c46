import React, { useState } from 'react';
import DescriptionInput from './DescriptionInput';


const SecondStep = ({ onBack, onGenerate }) => {
  const [styleDescription, setStyleDescription] = useState('');
  const [selectedStyles, setSelectedStyles] = useState([]);

  const styleOptions = [
    'Light', 'Dark', 'Modern', 'Artsy', 'Techy', 'Young',
    'Corporate', 'Formal', 'Elegant', 'Hand-drawn'
  ];

  const handleStyleToggle = (style) => {
    setSelectedStyles(prev =>
      prev.includes(style) ? prev.filter(s => s !== style) : [...prev, style]
    );
  };

  const handleGenerate = () => {

    onGenerate('Light');
  };



    return (
    
      <div className="content-wrapper">
        <div className="style-container">
        <label className="style-label">
          Describe the preferred style for your project
        </label>
        </div>

        <div>
        <DescriptionInput
          value={styleDescription}
          onChange={(e) => setStyleDescription(e.target.value)}
          placeholder="Green and minimalist"
          maxLength={150}
        />
        
        </div>

        <div className="style-chips">
          {styleOptions.map(style => (
            <button
              key={style}
              className={`style-chip ${selectedStyles.includes(style) ? 'selected' : ''}`}
              onClick={() => handleStyleToggle(style)}
            >
              {style}
            </button>
          ))}
        </div>
      
      <div className="button-container">
          <button className="back-button" onClick={onBack}>
            Go back
          </button>
          <button 
          className="generate-button"
          onClick={handleGenerate}
          // disabled={isLoading}
          >
            ✨Generate my project
            <span className="sprinkle"></span>
          </button>
        </div>
        
      <style jsx>{`
       
        .content-space {
         display: 'flex',
         flexDirection: 'column',
         gap: '24px',
        }  
         
        .style-label {
          display: 'block',
          color: '#333',
          fontSize: '20px', // Slightly larger font size for better readability
          fontWeight: '500',
          marginBottom: '12px',
        }
        

        

        .style-input:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
        }

        .character-count {
          text-align: right;
          color: #6B7280;
          font-size: 12px;
          margin-top: -12px;
        }

        .style-chips {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin: 8px 0;
        }

        .style-chip {
          background: white;
          border: 1px solid #E5E7EB;
          border-radius: 20px;
          padding: 8px 16px;
          font-size: 14px;
          color: #374151;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .style-chip.selected {
          background: #3B82F6;
          border-color: #3B82F6;
          color: white;
        }
        .button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.back-button {
  background: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
  color: #555;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s;
}
.back-button:hover {
  border-color: #888;
  color: #333;
}
.generate-button {
  position: relative;
  background: #6c63ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  overflow: hidden;
  transition: background 0.3s;
}
.generate-button:hover {
  background: #584cfc;
}

        

       

        
          



      `}</style>
    </div>
  );
};

export default SecondStep;