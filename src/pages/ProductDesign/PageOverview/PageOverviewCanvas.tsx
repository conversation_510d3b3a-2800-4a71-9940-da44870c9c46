/* eslint-disable jsx-a11y/iframe-has-title */
import React from "react";
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch";
import LaptopWithIframe from "../LaptopFrameComp";
import { Card } from "reactstrap";

interface PageviewCanvasProps {
  pages: any;
  navigateToChat: () => void;
  onPageSelected: (pageIndex: number) => void;
}
const PageviewCanvas: React.FC<PageviewCanvasProps> = ({
  pages,
  navigateToChat,
  onPageSelected
}) => {
  return (
    <TransformWrapper
      initialScale={1}
      initialPositionX={0}
      initialPositionY={0}
    >
      {({ zoomIn, zoomOut, resetTransform }) => (
        <React.Fragment>
          <TransformComponent
            wrapperStyle={{
              maxWidth: "100%",
              height: "90vh"
            }}
          >
            {/* <CurrentScale /> */}
            <div
              style={{
                display: "flex",
                background: "#0A0A0A",
                color: "white",
                // padding: "50px",
                height: "86vh",
                justifyContent: 'center',
                alignItems: 'center',
                width: "100vw"
              }}
            >
              {pages.map((page: any) => (
                <div
                  key={page.id}
                  id={`element-${page.id}`}
                  style={{ width: "250px", margin: "0 10px", textAlign:'center' }}
                  
                >
                  <Card style={{backgroundColor:'#0A0A0A'}} onClick={() => {
                    navigateToChat();
                    onPageSelected(page.id);
                  }}>
                  <LaptopWithIframe iframeSrc={page.code} isEditable={page.isEditable} isAddable={page.isAddable}></LaptopWithIframe>
                  <h2
                    style={{
                      color: "#fff"
                    }}
                  >
                    {page.name}
                  </h2>
                  </Card>
                  
                </div>
              ))}
            </div>
          </TransformComponent>
        </React.Fragment>
      )}
    </TransformWrapper>
  );
};

export default PageviewCanvas;
