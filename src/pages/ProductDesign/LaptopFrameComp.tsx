import React, { useState, useEffect, useRef } from "react";

interface LaptopProps {
  iframeSrc: string;
  isEditable: boolean;
  isAddable: boolean;
  onCodeUpdate?: (code: string) => void;
  onComponentSelect?: (componentInfo: string) => void; // New callback prop
  
}

type DOMElementType = HTMLElement;
type IframeElementType = HTMLIFrameElement;

const LaptopWithIframe = ({ iframeSrc, isEditable, isAddable , onCodeUpdate, onComponentSelect }: LaptopProps) => {
  const [selectedElement, setSelectedElement] = useState<DOMElementType | null>(null);
  const [extractedCode, setExtractedCode] = useState<string>('');
  const [savedCodes, setSavedCodes] = useState<string[]>([]); // Store history of saved codes
  const [selectedHtmlCode, setSelectedHtmlCode] = useState<string>(''); // New state variable
  const iframeRef = useRef<IframeElementType>(null);
  const [hoveredElement, setHoveredElement] = useState<DOMElementType | null>(null);
  const [selectionOverlay, setSelectionOverlay] = useState<{ x: number; y: number; width: number; height: number } | null>(null);

  // Function to format HTML with proper indentation
  const formatHTML = (html: string): string => {
    let formatted = '';
    let indent = 0;

    const temp = document.createElement('div');
    temp.innerHTML = html.trim();

    const format = (node: Node, level: number): void => {
      const indentString = '  '.repeat(level);

      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim() || '';
        if (text) {
          formatted += indentString + text + '\n';
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();

        const attributes = Array.from(element.attributes)
          .map(attr => `${attr.name}="${attr.value}"`)
          .join(' ');

        const attributeString = attributes ? ' ' + attributes : '';

        if (element.children.length) {
          formatted += `${indentString}<${tagName}${attributeString}>\n`;
          Array.from(element.children).forEach(child => format(child, level + 1));
          formatted += `${indentString}</${tagName}>\n`;
        } else {
          const content = element.textContent?.trim() || '';
          if (content) {
            formatted += `${indentString}<${tagName}${attributeString}>${content}</${tagName}>\n`;
          } else {
            formatted += `${indentString}<${tagName}${attributeString} />\n`;
          }
        }
      }
    };

    Array.from(temp.children).forEach(child => format(child, 0));
    return formatted;
  };

  // Function to auto-save code
  const autoSaveCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setSavedCodes(prev => [...prev, code]); // Store in history
      // console.log('Auto-saved code:', code);

      if (onCodeUpdate) {
        onCodeUpdate(code);
      }
    } catch (err) {
      console.error('Failed to auto-save code:', err);
    }
  };

  useEffect(() => {
    if (!isEditable && !isAddable) {
      if (hoveredElement) {
        hoveredElement.style.outline = '';
        setHoveredElement(null);
      }
      setSelectedElement(null);
      setExtractedCode('');
      setSelectionOverlay(null); // Reset overlay
      return;
    }

    const iframe = iframeRef.current;
    if (!iframe) return;

    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) return;

    const handleMouseOver = (e: MouseEvent): void => {
      if (!isEditable && !isAddable) return;
      e.stopPropagation();

      const target = e.target as DOMElementType;

      if (hoveredElement) {
        hoveredElement.style.outline = '';
      }

      target.style.outline = '2px solid #007bff';
      setHoveredElement(target);
    };

    const handleMouseOut = (e: MouseEvent): void => {
      if (!isEditable && !isAddable) return;
      const target = e.target as DOMElementType;
      target.style.outline = '';
    };

    const handleClick = (e: MouseEvent): void => {
      if (!isEditable && !isAddable) return;

      e.preventDefault();
      e.stopPropagation();

      const target = e.target as DOMElementType;
      setSelectedElement(target);

      if (hoveredElement) {
        hoveredElement.style.outline = '';
      }

      const formattedHTML = formatHTML(target.outerHTML);
      setExtractedCode(formattedHTML);
      setSelectedHtmlCode(formattedHTML); // Set the selected HTML code

      // Auto-save the code when element is selected
      autoSaveCode(formattedHTML);

      

      // Highlight the selected element
      target.classList.add('selected-element');

      // Update the overlay position and size
      const rect = target.getBoundingClientRect();
      setSelectionOverlay({
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height
      });

      // Call the onComponentSelect callback with the selected component's information
      if (onComponentSelect) {
        onComponentSelect(`You have selected this component: ${formattedHTML}`);
      }
    };

    iframeDoc.addEventListener('mouseover', handleMouseOver);
    iframeDoc.addEventListener('mouseout', handleMouseOut);
    iframeDoc.addEventListener('click', handleClick);

    return () => {
      iframeDoc.removeEventListener('mouseover', handleMouseOver);
      iframeDoc.removeEventListener('mouseout', handleMouseOut);
      iframeDoc.removeEventListener('click', handleClick);
    };
  }, [isEditable, isAddable, hoveredElement, onCodeUpdate, onComponentSelect]);

  return (
    <div className="relative w-full h-full">
      {/* Inspector Status */}
      <div className="absolute top-4 right-4 z-10">
        <div
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            isEditable || isAddable
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-800'
          }`}
        >
          {/* {isEditable ? 'Auto-save Active' : 'View Only Mode'} */}
        </div>
      </div>
      {/* Laptop SVG */}
      <div
      style={{
        width: "100%",
        height: "100%",
        position: "relative",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        // padding: "20px"
      }}

    >
      {/* Container for the SVG */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="190 170 820 470"
        preserveAspectRatio="xMidYMid meet"
        style={{
          width: "100%",
          height: "100%",
          maxHeight: "calc(100vh - 100px)" // Prevent overflow
        }}
      >
        <defs>
          {/* Gradient definition for the laptop screen */}
          <linearGradient
            id="screenGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop
              offset="0%"
              style={{ stopColor: "#FFFFFF", stopOpacity: 1 }}
            />
            <stop
              offset="50%"
              style={{ stopColor: "#F5F5F5", stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: "#F2F2F2", stopOpacity: 1 }}
            />
          </linearGradient>
        </defs>

        <g id="Laptop">
          {/* Screen Frame with lighter border */}
          <path
            style={{
              fill: "#F5F5F5", // Matching the screen area color
              stroke: "#DCDCDC", // Lighter stroke color for the border
              strokeWidth: 1, // Thinner border
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeMiterlimit: 10
            }}
            d="M922.761,604.503h-654.71V196.495c0-10.681,8.658-19.339,19.339-19.339h616.032c10.681,0,19.339,8.658,19.339,19.339V604.503z"
          />

          {/* Screen Content Area (solid white background) */}
          <rect
            x="294.558"
            y="204.93"
            style={{
              fill: "#FFFFFF", // Solid white color for the screen
              stroke: "#D3D3D3",
              strokeWidth: 1, // Thinner border
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeMiterlimit: 10
            }}
            width="610.924"
            height="385.793"
          />

          {/* Base Left */}
          <path
            style={{
              fill: "#F5F5F5",
              stroke: "#DCDCDC", // Lighter stroke color for base
              strokeWidth: 1, // Thinner border
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeMiterlimit: 10
            }}
            d="M193.995,604.579v4.827h317.332c3.225,0,6.427,0.459,9.566,1.207c0.369,0.09,0.741,0.169,1.117,0.238v-6.271H193.995z"
          />

          {/* Base Center (updated to resemble laptop's bottom) */}
          <path
            style={{
              fill: "#E0E0E0", // Matching the laptop's base color
              stroke: "#DCDCDC", // Lighter stroke color for base
              strokeWidth: 2, // Slightly thicker stroke for emphasis
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeMiterlimit: 10
            }}
            d="M657.99,604.579v6.271c-1.394,0.259-2.812,0.393-4.237,0.393H526.247c-1.425,0-2.843-0.134-4.237-0.393v-6.271H657.99z"
          />

          {/* Base Bottom */}
          <path
            style={{
              fill: "#F5F5F5",
              stroke: "#DCDCDC", // Lighter stroke color for base
              strokeWidth: 1, // Thinner border
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeMiterlimit: 10
            }}
            d="M513.021,609.405c0,0,6,0.412,9,1.412h126c0,0,4-1,8-1.412h319.371c0.112,0,0.136,0.161,0.029,0.194 c-28.36,8.779-57.879,13.245-87.569,13.245H590.021H272.189c-29.69,0-59.21-4.465-87.569-13.245 c-0.107-0.033-0.083-0.194,0.029-0.194H513.021"
          />

          {/* Base Right */}
          <path
            style={{
              fill: "#F5F5F5",
              stroke: "#DCDCDC", // Lighter stroke color for base
              strokeWidth: 1, // Thinner border
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeMiterlimit: 10
            }}
            d="M996.005,604.579v4.827H668.674c-3.225,0-6.427,0.459-9.566,1.207c-0.369,0.09-0.741,0.169-1.117,0.238v-6.271H996.005z"
          />
        </g>
          {/* Iframe inside the laptop screen */}
          <foreignObject x="284.558" y="194.93" width="630.924" height="395.793">
            <iframe
              ref={iframeRef}
              srcDoc={iframeSrc !== ""? iframeSrc :
              `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Bot Loader</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }
        @keyframes blink {
            0%, 100% { transform: scaleY(1); }
            50% { transform: scaleY(0.1); }
        }
        @keyframes antenna-glow {
            0%, 100% { box-shadow: 0 0 5px #60A5FA, 0 0 10px #60A5FA; }
            50% { box-shadow: 0 0 15px #60A5FA, 0 0 25px #60A5FA; }
        }
        @keyframes text-fade {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        @keyframes dots {
            0%, 20% { content: '.'; }
            40% { content: '..'; }
            60%, 100% { content: '...'; }
        }
        @keyframes wave {
            0%, 100% { transform: rotate(-5deg); }
            50% { transform: rotate(5deg); }
        }
        @keyframes pulse-ring {
            0% { transform: scale(0.7); opacity: 0.5; }
            50% { transform: scale(1); opacity: 0.3; }
            100% { transform: scale(0.7); opacity: 0.5; }
        }
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }
        .blink-animation {
            animation: blink 2s ease-in-out infinite;
        }
        .antenna-glow {
            animation: antenna-glow 1.5s ease-in-out infinite;
        }
        .text-fade {
            animation: text-fade 2s ease-in-out infinite;
        }
        .wave-animation {
            animation: wave 2s ease-in-out infinite;
        }
        .loading-dots::after {
            content: '';
            animation: dots 1.5s steps(1, end) infinite;
        }
        .pulse-ring::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: inherit;
            animation: pulse-ring 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
    </style>
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900">
    <div class="relative flex flex-col items-center">
        <!-- Pulsing Rings -->
        <div class="absolute -inset-4 bg-blue-500/20 rounded-full pulse-ring"></div>

        <!-- Bot Container -->
        <div class="float-animation">
            <!-- Antenna -->
            <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                <div class="w-1.5 h-6 bg-gradient-to-t from-blue-400 to-blue-300"></div>
                <div class="w-3 h-3 rounded-full bg-blue-300 antenna-glow"></div>
            </div>

            <!-- Head -->
            <div class="relative w-24 h-24 bg-gradient-to-b from-blue-400 to-blue-600 rounded-2xl shadow-lg shadow-blue-500/50">
                <!-- Eyes Container -->
                <div class="absolute top-8 w-full flex justify-around px-4">
                    <div class="w-4 h-4 bg-white rounded-full blink-animation"></div>
                    <div class="w-4 h-4 bg-white rounded-full blink-animation"></div>
                </div>

                <!-- Mouth -->
                <div class="absolute bottom-5 left-1/2 transform -translate-x-1/2 w-8 h-1.5 bg-white rounded-full"></div>
            </div>

            <!-- Body -->
            <div class="mt-2 w-22 h-28 bg-gradient-to-b from-blue-500 to-blue-700 rounded-xl shadow-lg shadow-blue-600/50">
                <!-- Chest Lights -->
                <div class="mt-4 flex justify-center space-x-2">
                    <div class="w-3 h-3 bg-blue-300 rounded-full antenna-glow"></div>
                    <div class="w-3 h-3 bg-blue-300 rounded-full antenna-glow" style="animation-delay: 0.5s"></div>
                    <div class="w-3 h-3 bg-blue-300 rounded-full antenna-glow" style="animation-delay: 1s"></div>
                </div>
            </div>
        </div>

        <!-- Loading Text -->
        <div class="mt-12 text-center">
            <p class="text-blue-300 text-lg font-semibold text-fade">
                <span>Gathering Information</span>
                <span class="loading-dots"></span>
            </p>
            <p class="text-blue-400/80 text-sm mt-2">Please wait while I process your request</p>
        </div>
    </div>
</body>
</html>`}
              title="Laptop Screen"
              width="630.924"
              height="395.793"
              style={{ border: "none" }}
              allow="clipboard-read; clipboard-write"
              id="iframeId"
            />
          </foreignObject>
        </svg>
      </div>
      {/* Overlay for selected element */}
        {/* {selectionOverlay && (
          // <div
          //   style={{
          //     position: 'absolute',
          //     left: selectionOverlay.x,
          //     top: selectionOverlay.y,
          //     width: selectionOverlay.width,
          //     height: selectionOverlay.height,
          //     // border: '2px solid #ff0000',
          //     backgroundColor: 'rgba(255, 0, 0, 0.2)',
          //     pointerEvents: 'none',
          //     zIndex: 1000
          //   }}
          // />
        )} */}
    </div>
  );
};

export default LaptopWithIframe;