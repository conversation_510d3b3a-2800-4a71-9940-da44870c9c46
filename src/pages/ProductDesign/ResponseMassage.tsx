import React, { useEffect, useCallback } from "react";
import { UserMessage } from "Mike/models/response";

interface ResponseMessageProps {
  message: UserMessage & {
    responseData?: {
      // design: string;
      code: string;
    } | null;
  };
  isActive: boolean;
  onClick: () => void;
  isEditMode: boolean;
  isAddMode: boolean;
  onUpdateMessage: (updatedMessage: UserMessage) => void;
}

const ResponseMessage: React.FC<ResponseMessageProps> = ({
  message,
  isActive,
  onClick,
  isEditMode,
  isAddMode,
  onUpdateMessage,
}) => {
  const messageText = message.msg || "";
  const messageDateTime = message.datetime || "";
  const isComponentSelection = messageText.includes("You have selected this component:");

  // Automatically update component selection message based on initial mode
  useEffect(() => {
    if (isComponentSelection) {
      const mode = isEditMode ? "Edit" : isAddMode ? "Add" : "None";
      const Reqmessage = isEditMode ? "Required Modifications" : isAddMode ? "Component will be rendered below" : "None";
      const expectedText = `You have selected this component: ${mode} Mode. ${Reqmessage}`;

      if (!messageText.includes(expectedText)) {
        const updatedMessage = { ...message, msg: expectedText };
        onUpdateMessage(updatedMessage);
      }
    }
  }, []); // Run only once on component mount

  return (
    <div
      key={message.id}
      className={`conversation-list ${isActive ? "bg-light" : ""} rounded-3`}
      onClick={onClick}
    >
      <div className="chat-avatar">
        <div className="avatar-xs">
          <div className="avatar-title rounded-circle bg-primary-subtle text-primary">
            <i className="ri-message-2-line"></i>
          </div>
        </div>
      </div>
      <div className="user-chat-content">
        <div className={`ctext-wrap ${isComponentSelection ? "border-start border-primary border-3" : ""}`}>
          <div className="ctext-wrap-content">
            {isComponentSelection ? (
              <div className="p-3">
                <div className="d-flex align-items-center mb-3">
                  <div className="flex-shrink-0">
                    <div className="avatar-xs">
                      <div className="avatar-title rounded-circle bg-primary-subtle text-primary">
                        <i className="ri-cursor-fill"></i>
                      </div>
                    </div>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    <h5 className="mb-1 text-primary">Component Selected</h5>
                  </div>
                </div>
                <div className="p-3">
                  <div className="d-flex align-items-center mb-2">
                    <i className="ri-code-s-slash-fill text-primary me-2"></i>
                    <span className="text-primary fw-semibold">{messageText.replace("You have selected this component:", "")}</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-3">
                <div className="d-flex align-items-center mb-2">
                  <i className="ri-code-s-slash-fill text-primary me-2"></i>
                  <span className="text-primary fw-semibold">Generated Response</span>
                </div>
                {/* <p className="mb-0 ctext-content">{messageText}</p> */}
              </div>
            )}
          </div>
        </div>
        <div className="conversation-name">
          <small className="text-muted time">{messageDateTime}</small>
          <span className="text-success check-message-icon">
            <i className="ri-check-double-line align-bottom"></i>
          </span>
        </div>
      </div>
    </div>
  );
};

export default React.memo(ResponseMessage);