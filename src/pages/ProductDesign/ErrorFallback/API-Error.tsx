import React from "react";
import { Col, Container, <PERSON>, But<PERSON> } from "reactstrap";
import comingSoon from "../../../assets/images/coming-soon-img.png";

interface APIErrorProps {
  onRetry: () => void;
  onBack: () => void;
}

const APIError: React.FC<APIErrorProps> = ({ onRetry, onBack } : APIErrorProps) => {
  return (
    <React.Fragment>
      <div className="auth-page-content page-content h-100  ">
        <div className="auth-page-wrapper py-5 d-flex justify-content-center align-items-center min-vh-50">
          <div className="auth-page-content overflow-hidden p-0">
            {/* <Container> */}
              <Row className="justify-content-center">
                <Col xl={7} lg={8}>
                  <div className="text-center">
                    <img
                      src={comingSoon}
                      alt="error img"
                      className="img-fluid"
                    />
                    <div className="mt-3">
                      <h3 className="text-uppercase">
                      Something Went Wrong😭
                      </h3>
                      <h4>Please try again</h4>
                      <Button
                        className=" m-2"
                        color="info"
                        onClick={onRetry}
                      >
                        <i className="ri-refresh-line me-1"></i>Retry
                      </Button>
                      <Button 
                        color="info" 
                        onClick={onBack}
                      >
                        <i className="ri-arrow-go-back-fill">&nbsp;</i>Back
                      </Button>
                    </div>
                  </div>
                </Col>
              </Row>
            {/* </Container> */}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default APIError;
