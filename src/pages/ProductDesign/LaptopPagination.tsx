import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

const LaptopPagination = ({
  pages,
  currentPageIndex,
  onNextPage,
  onPrevPage
}: {
  pages: string[];
  currentPageIndex: number;
  onNextPage: () => void;
  onPrevPage: () => void;
}) => {
  const currentPage = pages !== undefined? pages[currentPageIndex] : 0;

  return (
    <div
      className="d-flex justify-content-center align-items-center mt-3"
      style={{
        borderRadius: "20px",
        padding: "10px",
        backgroundColor: "transparent",
        position: "absolute",
        bottom: "10%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        zIndex:2
      }}
    >
      <button
        onClick={onPrevPage}
        disabled={currentPageIndex === 0}
        className={`p-1 rounded-full hover:bg-gray-200 transition-colors ${
          currentPageIndex === 0 ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        {/* <ChevronLeft size={20} /> */}
        <i
          className="ri-arrow-left-double-line"
          style={{
            fontSize: "20px",
            color: currentPageIndex === 1 ? "#666" : "#212121"
          }}
        ></i>
      </button>
      <span className="font-medium text-sm">{currentPage || "Page"}</span>
      <button
        onClick={onNextPage}
        disabled={currentPageIndex === (pages !== undefined ? pages.length - 1: 0)}
        className={`p-1 rounded-full hover:bg-gray-200 transition-colors ${
          currentPageIndex === (pages !== undefined ? pages.length - 1: 0)
            ? "opacity-50 cursor-not-allowed"
            : ""
        }`}
      >
        {/* <ChevronRight size={20} /> */}

        <i
          className="ri-arrow-right-double-line"
          style={{
            fontSize: "20px",
            color: currentPage ? "#666" : "#212121"
          }}
        ></i>
      </button>
    </div>
  );
};

export default LaptopPagination;
