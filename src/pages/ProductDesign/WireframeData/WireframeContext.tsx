// import React, { useState } from "react";
// import axios from "axios";
// import "./form.css";
// import { axiosInstance } from "Mike/utils/axiosConfig";
// import ProjectWizard from "../FrontPage/ProjectWizard";

// // Define types for form data and errors
// interface FormData {
//   device: string;
//   description: string;
// }

// interface FormErrors {
//   device?: string;
//   description?: string;
// }

// interface WireframeContextProps {
//   setFormData: (data: any) => void; // Prop from parent to set form data
// }

// const WireframeContext: React.FC<WireframeContextProps> = ({
//   setFormData,
// }) => {


//   return (
//     <div>
//       <ProjectWizard context={data} saveContext={data} handleGenerate={setFormData} ></ProjectWizard>
//     </div>
//   );
// };

// export default WireframeContext;
