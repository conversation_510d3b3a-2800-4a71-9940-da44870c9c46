/* body {
    margin: 0;
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #d9aaff, #b58aff);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  
  .container {
    background: #ffffff;
    color: #333;
    width: 90%;
    max-width: 600px;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }
  
  .header {
    padding: 16px;
    background: #f4f4f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .back-button {
    background: none;
    border: none;
    color: #333;
    cursor: pointer;
    font-size: 16px;
  }
  
  .content {
    padding: 24px;
    text-align: center;
  }
  
  h1 {
    font-size: 20px;
    margin-bottom: 24px;
  }
  
  .form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .device-options {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
  
  .device-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
  }
  
  .description-label {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid #ccc;
  }
  
  textarea:focus {
    outline: none;
    border-color: #7b47f6;
  }
  
  .continue-button {
    background: #7b47f6;
    color: #fff;
    border: none;
    padding: 12px;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
  }
  
  .continue-button:hover {
    background: #6a3fe0;
  }
  
  .error {
    color: red;
    font-size: 14px;
  }
  
  @media (max-width: 768px) {
    .content {
      padding: 16px;
    }
  }
   */