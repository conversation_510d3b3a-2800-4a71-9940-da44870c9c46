/* eslint-disable @typescript-eslint/no-unused-vars */
import { UserMessage, userMessagesType } from "Mike/models/response";
import { axiosInstance } from "Mike/utils/axiosConfig";
import AOS from "aos";
import "aos/dist/aos.css";
import axios from "axios";
import React, { useEffect, useRef, useState, KeyboardEvent } from "react";
import "react-perfect-scrollbar/dist/css/styles.css";
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
// import ReactLoading from "react-loading";
import {
  Button,
  Card,
  CardBody,
  CardTitle,
  Col,
  Container,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalBody,
  ModalHeader,
  Progress,
  Row,
  UncontrolledDropdown
} from "reactstrap";
import SimpleBar from "simplebar-react";
import mlo from "../../assets/images/mikeLogo.png";
import userDummayImage from "../../assets/images/users/avatar-1.jpg";

import "./animation.css";
import ResponseMessage from "./ResponseMassage";
import { ThreeDot } from "react-loading-indicators";

// import WebMobilePage from "Components/Common/WebMobilePage";
import LaptopWithIframe from "./LaptopFrameComp";
import { useUser } from "../../context/UserContext";
import ReactMarkdown from "react-markdown";
import ProjectWizard from "./FrontPage/ProjectWizard";
import PageviewCanvas from "./PageOverview/PageOverviewCanvas";
import APIError from "./ErrorFallback/API-Error";


interface Page {
  id: number;
  name: string;
  code: string;
}

// Define the type for your state object
interface ChatState {
  conversations: any[];
  loading: boolean;
  error: string | null;
  generatedDesign?: {
    design: string;
    code: string;
  };
}
interface APIResponse {
  response: {
    id: string;
    source: string;
    executedPrompt: string;
    choices: {
      text: string;
      index: string;
      identifier: string;
    }[];
  };
}
interface Choice {
  text: string;
  index: string;
  identifier: string;
}

interface ResponseData {
  response: {
    id: string;
    source: string;
    executedPrompt: string;
    object: string;
    choices: Choice[];
    references: any[];
  };
}

const ChatInterface = ({ pages, setPages, index, setPageState }: { pages: Page[], setPages: React.Dispatch<React.SetStateAction<Page[]>>, index: number, setPageState: React.Dispatch<React.SetStateAction<"context" | "pages" | "chat">> }) => {
  const userChatShow: any = useRef();
  const dispatch = useDispatch<any>();
  const [Chat_Box_Username] = useState<any>("Michelangelo");
  const [user_Status] = useState<string | null>("Wireframe Creator");

  const [Chat_Box_Image] = useState<any>(mlo);
  const [currentRoomId] = useState<any>(1);
  const [curMessage, setcurMessage] = useState<string>("");
  const [isLoading, setLoading] = useState(false);
  const [showGrapesPage, setShowGrapesPage] = useState(false);
  const [progress, setProgress] = useState(0);
  const [designReady, setDesignReady] = useState<boolean>(false);
  const [contextReady, setContextReady] = useState<boolean>(false);
  const [context, setContext] = useState<string>("");
  const [currentViewCode, setCurrentViewCode] = useState<any>([]);
  const [apiResponse, setApiResponse] = useState<APIResponse>({
    response: {
      id: "",
      source: "",
      executedPrompt: "",
      choices: []
    }
  });
  const [messages, setMassage] = useState([
    "Generating your design...",
    "Finding the right components...",
    "Have a cup of coffe while we finalise the design...",
    "Finalizing..."
  ]);
  const [view, setView] = useState(false);
  const navigate = useNavigate();
  const [chatMessages, setChatMessages] = useState<any>([
    {
      id: 1,
      roomId: 1,
      sender: "1",
      createdAt: "",
      usermessages: []
    }
  ]);

  const [chatState, setChatState] = useState<ChatState>({
    conversations: [],
    loading: false,
    error: null
  });
  const [showWebMobilePage, setShowWebMobilePage] = useState(true);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  const [generatedResponse, setGeneratedResponse] = useState<{
    code: string;
  } | null>(null);
  // Add this to your state declarations
  const [activeResponse, setActiveResponse] = useState(null);
  const { accounts } = useUser();
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [selectedComponentInfo, setSelectedComponentInfo] = useState<string>("");
  const [editModeMessages, setEditModeMessages] = useState<any>([
    {
      id: 1,
      roomId: 1,
      sender: "1",
      createdAt: "",
      usermessages: []
    }
  ]);

  const [addModeMessages, setAddModeMessages] = useState<any>([
    {
      id: 1,
      roomId: 1,
      sender: "1",
      createdAt: "",
      usermessages: []
    }
  ]);
  const [alertMessage, setAlertMessage] = useState<string | null>(null);
  const [selectedVersionCode, setSelectedVersionCode] = useState<string | null>(null);
  


  React.useEffect(() => {
    AOS.init({ duration: 800 }); // You can adjust the duration of the animation here
  }, []);

  // console.log("Generated Response from  siteMap Modal: ", generatedResponse);

  const [closeCard, setCloseCard] = useState(false);

  const toggleModal = () => {
    setView(false);
  };

  const viewDesign = () => {
    setView(true);
  };

  const toggleFullScreen = () => {
    const iframe = document.querySelector("iframe");
    if (iframe) {
      if (!document.fullscreenElement) {
        iframe.requestFullscreen().catch((err) => {
          console.error(
            `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
          );
        });
      } else {
        if (document.fullscreenElement) {
          document.exitFullscreen();
        }
      }
    }
  };
  const messagesEndRef = useRef<any>(null);
  useEffect(() => {
    if (messagesEndRef.current?.el) {
      
    messagesEndRef.current.getScrollElement().scrollTop = messagesEndRef.current.getScrollElement().scrollHeight;
    }
  }, [chatMessages])

  const handleEditClick = () => {
    setIsEditMode(!isEditMode);
    // setIsEditMode(true);
    setIsAddMode(false);
    // toggleFullScreen()
    setAlertMessage(null); 
    console.log("inside HandlEdit" , isEditMode )
    if (!isEditMode) {
      toggleFullScreen();
    }
  };
  const handleAddClick = () => {
    setIsAddMode(!isAddMode);
    setIsEditMode(false);
    // toggleFullScreen();
    setAlertMessage(null); 
    if (!isAddMode) { toggleFullScreen(); }
  }

  // const scrollToBottom = () => {
  //   if (messagesEndRef.current) {
  //     const { scrollHeight, clientHeight } = messagesEndRef.current;
  //     messagesEndRef.current.scrollTop = scrollHeight - clientHeight;
  //   }
  // };
  // useEffect(() => {
  //   scrollToBottom();
  // }, [chatState.conversations]); // This will trigger whenever messages
  const extractTextFromResponse = async (data: any) => {
    return data.data.response.choices[0].text;
  };

  useEffect(() => {
    console.log(curMessage);
  }, [curMessage]);

  const handleResponse = async (response: any) => {
    try {
      const text = response;
      if (!text) {
        throw new Error("Failed to extract text from response");
      }
      // Add assistant message to chat
      const assistantMessage = {
        id: Date.now() + 1,
        to_id: 1,
        msg: text,
        datetime: new Date().toLocaleString(),
        isResponse: true,
        responseData: {
          // design: response.data?.design,
          code: text
        }
      };

      // Update chat messages with assistant response
      setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
        {
          ...prev[0],
          usermessages: [...prev[0].usermessages, assistantMessage]
        }
      ]);

      return text;
    } catch (error) {
      console.error("Error handling response:", error);
      throw error;
    }
  };

  const handleComponentSelect = (componentInfo: string) => {
    const htmlCode = componentInfo.replace("You have selected this component: ", "");
    // console.log("htmlCode:", htmlCode);
    // console.log("Component selected:", componentInfo);
    setSelectedComponentInfo(htmlCode);
    const newMessage = {
      id: Date.now(),
      to_id: 1,
      msg: componentInfo,
      datetime: new Date().toLocaleString(),
      isResponse: true
    };

    setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
      {
        ...prev[0],
        usermessages: [...prev[0].usermessages, newMessage]
      }
    ]);
    console.log("inside component select before", isEditMode, isAddMode);
    // if (isEditMode) {
    //   setIsEditMode(true);
    //   setIsAddMode(false);
    //   // document.exitFullscreen();
    // } else if (isAddMode) {
    //   setIsEditMode(false);
    //   setIsAddMode(true);
    //   // document.exitFullscreen();
    // }
    // setIsEditMode(false);
    if(isEditMode){
      setIsEditMode(true);
      // toggleFullScreen();
      // document.exitFullscreen();
    }
    if(isAddMode){
      setIsAddMode(true);
    }
    console.log("inside component select after", isEditMode, isAddMode);
    if(document.fullscreenElement){
    document.exitFullscreen();}
  };

  const sendMessage = async () => {
    // console.log("User message: ", curMessage);
    if (!curMessage.trim()) return;
    if (!isEditMode && !isAddMode) {
      setAlertMessage("Please select a mode (Edit or Add) before continuing.");
      const alertMsg = {
        id: Date.now(),
        to_id: 1,
        msg: "Please select a mode (Edit or Add) before continuing.",
        datetime: new Date().toLocaleString(),
        isResponse: false
      };
      
    setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
      {
        ...prev[0],
        usermessages: [...prev[0].usermessages, alertMsg]
      }
    ]);
    return;
  }
    setcurMessage("");
    try {
      setLoading(true);
      // setShowWebMobilePage(false);

      // Add user message to chat
      const userMessage = {
        id: Date.now(),
        to_id: 2,
        msg: curMessage,
        datetime: new Date().toLocaleString()
      };

      // Update chat messages with user message
      setChatMessages((prev: { usermessages: any }[]) => [
        {
          ...prev[0],
          usermessages: [...prev[0].usermessages, userMessage]
        }
      ]);

      // console.log("Chat messages: ", chatMessages, curMessage, userMessage);

      // Make API call
      // const response = await axiosInstance.post(
      //   "/wireframe/create_wireframe/chat",
      //   {
      //     mode: "MLO_DESIGN_CHAT_AGENT",
      //     useCaseIdentifier:
      //       "MLO_DESIGN_CHAT_AGENT@ASCENDION@EXPERIENCE_ENGINEERING@MICHELANGELO@CAPEX",
      //     userSignature: accounts[0].username,
      //     conversations: [
      //       ...chatState.conversations,
      //       { role: "user", content: curMessage }
      //     ]
      //   }
      // );
      // const currentPageCode = pages[index].code;
      // const modifiedCode = selectedComponent ? 
      //   currentPageCode.replace(selectedComponent, `<!-- Modified component -->\n${curMessage}`) :
      //   currentPageCode;

      // console.log("selectedComponentInfo: ", selectedComponentInfo);
      // const apiPayload = {
      //   prompt: selectedComponentInfo
      //     ? `\nHere is my previously generated HTML code:\n${selectedComponentInfo}\n Please make the following changes : \n ${curMessage}`
      //     : `\nHere is my previously generated HTML code:\n${pages[index].code}\n Please make the following changes : \n ${curMessage}`
      // };
      const apiPayload = {
        // prompt: `\nHere is my previously generated HTML code:\n${pages[index].code}\n Here is my HTML code for selected component:\n${selectedComponentInfo}\n Please make the following changes on the selected component: \n ${curMessage}`
        // prompt: `\nHere is my HTML code for selected component:\n${selectedComponentInfo}\n Please make the following changes on the selected component: \n ${curMessage}`
        userSignature:accounts[0].username,
        prompt: `${curMessage}`,
        code: `${selectedVersionCode ?? pages[index].code}`,
        section_code: `${selectedComponentInfo}`
      };
      console.log('.Selected Version ', selectedVersionCode);
      const apiEndPoint = isEditMode ? "/wireframe/WIREFRAME-EDITOR/edit" : "/wireframe/WIREFRAME-EDITOR/add";
      console.log("State ", isEditMode, isAddMode);
      const response = await axiosInstance.post(
        apiEndPoint,
        apiPayload
      );
      if (response.data.status_code === 200) {
        const code = response.data.code;
        setSelectedVersionCode(null)
        // const updatedHtmlContent = extractHtmlContent(code);

        // const updatedHtmlContent = extractHtmlContent(code);

        // // Replace the selected HTML code with the extracted HTML from the response
        // const updatedPageCode = pages[index].code.replace(selectedComponentInfo, updatedHtmlContent);

        // setPages((prevPages) =>
        //   prevPages.map((page, i) =>
        //     i === index ? { ...page, code: updatedPageCode } : page
        //   )
        // );

        // setChatState((prev) => ({
        //   ...prev,
        //   conversations: [
        //     ...prev.conversations,
        //     { role: "user", content: curMessage },
        //     { role: "assistant", content: updatedHtmlContent }
        //   ]
        // }));

        // const assistantMessage = {
        //   id: Date.now() + 1,
        //   to_id: 1,
        //   msg: updatedHtmlContent,
        //   datetime: new Date().toLocaleString(),
        //   isResponse: true,
        //   responseData: {
        //     code: updatedHtmlContent
        //   }
        // };

        // setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
        //   {
        //     ...prev[0],
        //     usermessages: [...prev[0].usermessages, assistantMessage]
        //   }
        // ]);

        // TODO Update the pages state variable with the updated code
      } else {
        throw new Error("Failed to fetch data: Invalid status code");
      }

      const code = response.data.code;
      console.log(" response code: ", code);
      setApiResponse(response.data);
      setSelectedVersionCode(null)
      await handleResponse(extractHtmlContent(code));
      // const updatedHtmlContent = extractHtmlContent(code);
      // const updatedPageCode = pages[index].code.replace(selectedComponentInfo, updatedHtmlContent);
      setPages((prevPages) =>
        prevPages.map((page, i) =>
          i === index ? { ...page, code: code } : page
        )
      );
      // setChatState((prev) => ({
      //     ...prev,
      //     conversations: [
      //       ...prev.conversations,
      //       { role: "user", content: curMessage },
      //       { role: "assistant", content: updatedHtmlContent }
      //     ]
      //   }));

      //   const assistantMessage = {
      //     id: Date.now() + 1,
      //     to_id: 1,
      //     msg: updatedHtmlContent,
      //     datetime: new Date().toLocaleString(),
      //     isResponse: true,
      //     responseData: {
      //       code: updatedHtmlContent
      //     }
      //   };

      //   setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
      //     {
      //       ...prev[0],
      //       usermessages: [...prev[0].usermessages, assistantMessage]
      //     }
      //   ]); 
      //   ...prev,
      //   conversations: [
      //     ...prev.conversations,
      //     { role: "user", content: curMessage },
      //     { role: "assistant", content: response.data.response }
      //   ]
      // }));
    } catch (error) {
      console.error("Error in sendMessage:", error);

      // Add error message to chat
      const errorMessage = {
        id: Date.now(),
        to_id: 1,
        msg: "Sorry, I encountered an error. Please try again.",
        datetime: new Date().toLocaleString()
      };

      setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
        {
          ...prev[0],
          usermessages: [...prev[0].usermessages, errorMessage]
        }
      ]);
    } finally {
      setLoading(false);
      setShowWebMobilePage(false);
    }
  };

  useEffect(() => {
    console.log("Pages updated automatically", pages);
  }, [pages]);

  const extractHtmlContent = (html: string): string => {
    const match = html.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
    return match ? match[0] : "";
  };

  const handlePrevPage = () => {
    if (currentPageIndex > 0) {
      setCurrentPageIndex((prev) => prev - 1);
    }
  };

  const handleResponseClick = (response: any) => {
    if (response.responseData) {
      setActiveResponse(response.id);
      setGeneratedResponse(response.responseData);
      setSelectedVersionCode(response.responseData.code);
      console.log("selected version" , selectedVersionCode)

      // Update iframe content
      const iframe = document.querySelector("iframe");
      if (iframe) {
        iframe.srcdoc = response.responseData.code;
        // setSelectedVersionCode(response.responseData.code);
      }
    }
  };
  // const chatRef = useRef<any>(null);
  // Update the keyboard event handler
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // Prevent default to avoid line break
      if (curMessage.trim() !== "") {
        sendMessage();
      }
    }
  };

  // const handleComponentSelect = (componentInfo: string) => {
  //   // Add a new message to the chat indicating the selected component
  //   const htmlCode = componentInfo.replace("You have selected this component: ", "");
  //   console.log("htmlCode:", htmlCode);
  //   console.log("Component selected:", componentInfo);
  //   const newMessage = {
  //     id: Date.now(),
  //     to_id: 1,
  //     msg: componentInfo,
  //     datetime: new Date().toLocaleString(),
  //     isResponse: true
  //   };

  //   setChatMessages((prev: { usermessages: UserMessage[] }[]) => [
  //     {
  //       ...prev[0],
  //       usermessages: [...prev[0].usermessages, newMessage]
  //     }
  //   ]);
  // };

  const [, setemojiArray] = useState<any>([]);

  const [grapesContent, setGrapesContent] = useState({ code: "" });

  // const handleEditClick = async () => {
  //   setShowGrapesPage(true);
  //   if (pages[index]) {
  //     setGrapesContent({
  //       code: pages[index].code
  //       // design: pages[index].code
  //     });
  //   } else {
  //     console.log("No API response available");
  //   }
  // };

  const messageRefs = useRef<{ [key: number]: HTMLLIElement | null }>({});

  // const updateMessage = (messageId: number, newText: string) => {
  //   if (messageRefs.current[messageId]) {
  //     messageRefs.current[messageId]!.innerText = newText; // Update the text directly
  //   }
  // };

  const updateMessage = (updatedMessage: UserMessage) => {
    setChatMessages((prevMessages : any) =>
      prevMessages.map((message: { usermessages: UserMessage[] }) => ({
        ...message,
        usermessages: message.usermessages.map((userChat: UserMessage) =>
          userChat.id === updatedMessage.id ? updatedMessage : userChat
        ),
      }))
    );
  };
  


  return (
    <React.Fragment>
      <Container fluid>
        <Card>
          <div
            style={{
              position: "relative",
              height: "95vh",
              overflow: "hidden"
            }}
          >
            <div
              key="chatInterface"
              style={{
                height: "100%",
                position: "absolute",
                width: "100%",
                top: 0,
                left: 0,
                zIndex: 1
              }}
            >
              <div className="d-flex" style={{ height: "100%" }}>
                {/* Left section for iframe - 75% width */}

                <div style={{ width: "75%" }}>
                  <Card
                    style={{
                      // height: "calc(100vh - 86px)",
                      borderRadius: "15px",
                      backgroundColor: "#f1f1f1",
                      position: "relative",
                      zIndex: 200
                    }}
                  >

                    <h3 className=" d-flex justify-content-center " style={{ margin: 0 }}>
                      {pages[index].name}
                    </h3>
                    <LaptopWithIframe iframeSrc={selectedVersionCode ?? pages[index].code} isEditable={isEditMode} isAddable={isAddMode}
                      onComponentSelect={handleComponentSelect} />
                    {/* <UncontrolledDropdown
                      className="message-box-drop"
                      style={{
                        position: "absolute",
                        top: "10px",
                        right: "10px",
                        zIndex: 1
                      }}
                    > */}
                    {/* <DropdownToggle href="#" className="btn nav-btn" tag="a">
                        <i className="ri-more-fill"></i>
                      </DropdownToggle>
                      <DropdownMenu> */}
                    {/* <DropdownItem href="#" onClick={viewDesign}>
                            <i className="ri-eye-line me-2 text-muted align-bottom"></i>
                            View
                          </DropdownItem> */}
                    {/* <DropdownItem href="#" onClick={handleEditClick}>
                        <i
                        className={`ri-pencil-line me-2 align-bottom ${isEditMode ? 'text-primary' : 'text-muted'}`}
                        style={{ cursor: "pointer" }}

                        onClick=
                        {() => {
                          toggleFullScreen();
                          handleEditClick();
                        }}
                      ></i>
                          Edit
                        </DropdownItem>
                        <DropdownItem href="#" onClick={toggleFullScreen}> */}
                    {/* <i className="ri-fullscreen-line me-2 text-muted align-bottom"></i>
                          Full Screen
                        </DropdownItem>
                      </DropdownMenu>
                    </UncontrolledDropdown> */}
                    {/* <div
                      className="border-black"
                      style={{
                        position: "absolute",
                        top: "10px",
                        left: "10px",
                        padding: "8px",
                        fontSize: "24px"
                      }}
                    >
                      <i
                        aria-label="back buttton"
                        className="px-3 py-3 mx-2 ri-arrow-left-line rounded-pill shadow-lg back-btn"
                        onClick={() => setPageState("pages")}
                        style={{ cursor: "pointer" }}
                      ></i>
                    </div> */}
                    <div
                      className="border-black"
                      style={{
                        position: "absolute",
                        top: "10px",
                        right: "10px",
                        padding: "8px",
                        fontSize: "24px"
                      }}
                    >
                      <i className="ri-fullscreen-line me-2 text-muted align-bottom" style={{ cursor: "pointer" }} onClick={toggleFullScreen}></i>
                      <i
                        className={`ri-pencil-line me-2 align-bottom ${isEditMode ? 'text-primary' : 'text-muted'}`}
                        style={{ cursor: "pointer" }}

                        onClick=
                        {() => {
                          // toggleFullScreen();
                          handleEditClick();
                        }}
                      ></i>
                      <i
                        className={`ri-add-line me-2 align-bottom ${isAddMode ? 'text-primary' : 'text-muted'}`}
                        style={{ cursor: "pointer" }}
                        onClick={() => {
                          // toggleFullScreen();
                          // handleAddClick();
                          handleAddClick();
                        }}
                      ></i>

                    </div>

                    <div
                      className="border-black"
                      style={{
                        position: "absolute",
                        top: "10px",
                        left: "10px",
                        padding: "8px",
                        fontSize: "24px"
                      }}
                    >
                      <i
                        aria-label="back buttton"
                        className="px-3 py-3 mx-2 ri-arrow-left-line rounded-pill shadow-lg back-btn"
                        onClick={() => setPageState("pages")}
                        style={{ cursor: "pointer" }}
                      ></i>
                    </div>
                    {/* </Card>
                  </div> */}
                  </Card>
                </div>

                {/* Right section for chat - 25% width */}
                <div
                  className="user-chat shadow-lg"
                  style={{ width: "25%", backgroundColor: "##D3EFEB" }}
                  ref={userChatShow}
                >
                  <div className="h-100 chat-content" >
                    <div className="w-100 overflow-hidden h-100">
                      <div className="h-100 d-flex" style={{ flexDirection: 'column' }}>
                        {/* Chat header */}
                        <div className="p-3 user-chat-topbar bg-secondary-subtle" >
                          <Row className="align-items-center">
                            <Col>
                              <div className="d-flex align-items-center">
                                <div className="flex-grow-1 overflow-hidden">
                                  <div className="d-flex align-items-center">
                                    <div className="flex-shrink-0 chat-user-img online user-own-img align-self-center me-3 ms-0">
                                      {Chat_Box_Image === undefined ? (
                                        <img
                                          src={userDummayImage}
                                          className="rounded-circle avatar-xs"
                                          alt=""
                                        />
                                      ) : (
                                        <img
                                          src={Chat_Box_Image}
                                          className="rounded-circle avatar-xs"
                                          alt=""
                                        />
                                      )}
                                      <span className="user-status"></span>
                                    </div>
                                    <div className="flex-grow-1 overflow-hidden">
                                      <h5 className="text-truncate mb-0 fs-16">
                                        <a
                                          className="text-reset username"
                                          data-bs-toggle="offcanvas"
                                          href="#userProfileCanvasExample"
                                          aria-controls="userProfileCanvasExample"
                                        >
                                          {Chat_Box_Username}
                                        </a>
                                      </h5>
                                      <p className="text-truncate text-muted fs-14 mb-0 userStatus">
                                        <small>
                                          {user_Status === null
                                            ? "24 Members"
                                            : user_Status}
                                        </small>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </Col>
                          </Row>
                        </div>

                        {/* Chat messages area */}
                        <div id="users-chat" style={{ flex: '0 0 68%' }}>
                          <div
                            className="chat-conversation p-2"
                            id="chat-conversation"
                          >
                            <SimpleBar
                              ref={messagesEndRef}
                              style={{
                                height: "calc(100vh - 250px)",
                                marginBottom: "20px"
                              }}
                            >
                                <ul
                                  className="list-unstyled chat-conversation-list my-5"
                                  id="users-conversation"
                                >
                                  {/* Chat messages rendering */}
                                  {(chatMessages || []).map(
                                    (message: userMessagesType) =>
                                      message.usermessages.map(
                                        (userChat: UserMessage, key: number) => (
                                        
                                          // <li
                                          //   className={
                                          //     userChat.to_id === 1
                                          //       ? "chat-list left"
                                          //       : "chat-list right"
                                          //   }
                                          //   key={key}
                                          // >
                                          <li
                                            ref={(el) => (messageRefs.current[userChat.id] = el)}
                                            className={userChat.to_id === 1 ? "chat-list left" : "chat-list right"}
                                            key={userChat.id}
                                          >
                                            {userChat.isResponse ? (
                                              <ResponseMessage
                                                message={userChat}
                                                isActive={
                                                  activeResponse === userChat.id
                                                }
                                                onClick={() =>
                                                  handleResponseClick(userChat)
                                                }
                                                isEditMode={isEditMode}
                                                isAddMode={isAddMode}
                                                // unqiueKey={userChat.id}
                                                // messageKey={message.id}
                                                onUpdateMessage={updateMessage} 

                                              />
                                            ) : (
                                              <div className="conversation-list">
                                                {message.sender ===
                                                Chat_Box_Username &&
                                                userChat.to_id === 1 && (
                                                  <div className="chat-avatar">
                                                    {Chat_Box_Image ===
                                                      undefined ? (
                                                      <img
                                                        src={userDummayImage}
                                                        alt=""
                                                      />
                                                    ) : (
                                                      <img
                                                        src={Chat_Box_Image}
                                                        alt=""
                                                      />
                                                    )}
                                                  </div>
                                                )}
                                              <div className="user-chat-content">
                                                <div className="ctext-wrap">
                                                  <div className="ctext-wrap-content">
                                                    {/* <p className="mb-0 ctext-content"> */}
                                                    <ReactMarkdown>
                                                      {userChat.msg}
                                                    </ReactMarkdown>
                                                    {/* </p> */}
                                                    {/* {isEditMode && userChat.id && <p className="text-muted mb-0">Ready for modifications</p>} */}
                                                    {/* {isAddMode && userChat.id && <p className="text-muted mb-0">New section added below the component</p>} */}
                                                  </div>
                                                </div>
                                                <div className="conversation-name">
                                                  <small className="text-muted time">
                                                    {userChat.datetime}
                                                  </small>
                                                  <span className="text-success check-message-icon">
                                                    <i className="ri-check-double-line align-bottom"></i>
                                                  </span>
                                                </div>
                                              </div>
                                            </div>
                                          )}
                                        </li>
                                      )
                                    )
                                )}
                                {/* Loading indicator */}
                                {isLoading && (
                                  <div
                                    style={{
                                      paddingLeft: 30,
                                      paddingBottom: 10
                                    }}
                                  >
                                    <div className="text-muted mb-0">
                                      {contextReady && (
                                        <p>{messages[currentMessageIndex]}</p>
                                      )}
                                    </div>
                                    <ThreeDot
                                      variant="bounce"
                                      color="#32cd32"
                                      size="small"
                                      text=""
                                      textColor=""
                                    />
                                  </div>
                                )}
                              </ul>
                            </SimpleBar>
                          </div>
                        </div>

                        {/* Chat input section */}
                        <div
                          className="chat-input-section p-3"
                          style={{ backgroundColor: "#f5f5f5", flex: 1 }}
                        >
                          <form id="chatinput-form">
                            <Row className="g-0 align-items-center">
                              <div className="col">
                                <div className="chat-input-feedback">
                                  Please Enter a Message
                                </div>
                                <input
                                  type="text"
                                  value={curMessage}
                                  onKeyDown={handleKeyPress}
                                  onChange={(e) =>
                                    setcurMessage(e.target.value)
                                  }
                                  className="form-control chat-input bg-white border-light"
                                  id="chat-input"
                                  placeholder="Type your message..."
                                />
                              </div>
                              <div className="col-auto">
                                <div className="chat-input-links ms-2">
                                  <div className="links-list-item">
                                    <button
                                      type="button"
                                      disabled={curMessage === ""}
                                      onClick={() => {
                                        sendMessage();
                                        setemojiArray("");
                                      }}
                                      className="btn btn-success chat-send waves-effect waves-light"
                                    >
                                      <i className="ri-send-plane-2-fill align-bottom"></i>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </Row>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </Container>
    </React.Fragment>
  );
};

const GenerateWireFrame = () => {
  const [formData, setFormData] = useState<any>(null); // Holds the data from the API call
  const [pageState, setPageState] = useState<"context" | "pages" | "chat">(
    "context"
  ); // Controls which page to render
  const [selectedPage, setSelectedPage] = useState<any>(null);
  const [pages, setPages] = useState<Page[]>([{ id: 0, name: "", code: "" }]);
  const [viewPage, setViewPage] = useState<number>(0);
  const [error, setError] = useState<boolean>(false);
  const { accounts } = useUser(); // Assuming you have a hook to get user accounts
  // Function to handle form data update and additional API call
  const updateFormData = async (data: string) => {
    try {
      setError(false); // Reset error state
      // const data = description + ". I need the website in" + style + "theme";
      setFormData(data); // Set the form data

      // Make the first API call for description
      const response = await axiosInstance.post(
        "/wireframe/create_wireframe/generate_description",
        {
          description: data,
          userSignature:accounts[0].username,
        }
      );

      // Extract the processedPages and remainingPages
      const processedPages = response.data.response.processedPages;
      const remainingPages = response.data.response.remainingPages;

      // Initialize the pages with processed pages
      setPages(processedPages);

      // Move to the "pages" state
      setPageState("pages");

      // If there are remainingPages, continue making API calls until it's empty
      let remaining = { ...remainingPages }; // Create a copy of remainingPages to modify in the loop
      while (remaining.pages.length > 0) {
        // Make the subsequent API call for remaining pages
        const remaining_response = await axiosInstance.post(
          "/wireframe/create_wireframe/generate_v3",
          remaining
        );

        // Handle the response, update remainingPages from the response
        const remainingResult = remaining_response.data.response;
        console.log("API Response:", remainingResult);

        // Update IDs for the new pages based on the current number of pages
        setPages((prevPages) => {
          const currentLength = prevPages.length; // Get the current number of pages
          const updatedPages = remainingResult.processedPages.map(
            (page: Page, index: number) => ({
              ...page,
              id: currentLength + index // Assign a unique ID based on the current page count
            })
          );

          return [...prevPages, ...updatedPages]; // Append the updated pages to the state
        });

        // Update remainingPages for the next iteration (if any)
        remaining = remainingResult.remainingPages || []; // If the response has new remaining pages, update
      }

      console.log("All API calls completed, remainingPages is empty now.");
    } catch (error) {
      // TODO display error page and retry option
      console.error("Error in API call:", error);
      setError(true);
    }
  };

  useEffect(() => {
    console.log();
  }, [pages]);

  // Handle navigation between states
  const handlePageNavigation = (state: "context" | "pages" | "chat") => {
    setPageState(state);
  };

  const handlePageSelection = (selectedPageIndex: number) => {
    setViewPage(selectedPageIndex); // Update the viewPage in parent component
  };

  return (
    <>
      {error ? (
        <div className="page-content">
          <APIError
            onRetry={() => updateFormData(formData)}
            onBack={() => {
              setError(false);
              setPageState("context");
            }}
          /></div>
      ) : (
        <>
          {pageState === "context" && (
            <div className="page-content" style={{ justifyContent: 'center', alignItems: 'center', height: '100dvh', display: 'flex' }}>
              <ProjectWizard handleGenerate={updateFormData}></ProjectWizard></div>

            // {/* // </div> */}
          )}
          {pageState === "pages" && (
            <div className="page-content" style={{ minHeight: '100vh' }}>
              <PageviewCanvas
                pages={pages}
                navigateToChat={() => handlePageNavigation("chat")}
                onPageSelected={handlePageSelection}
              />
            </div>

          )}
          {pageState === "chat" && (
            <div className="page-content">
              {/* <Button onClick={() => setPageState("pages")}>Back</Button> */}
              <ChatInterface pages={pages} setPages={setPages} index={viewPage} setPageState={setPageState} />
            </div>
          )}
        </>
      )}
    </>
  );
};

export default GenerateWireFrame;