import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const Navdata = () => {
  const history = useNavigate()
  //state data
  const [isDashboard] = useState<boolean>(false)
  const [isHome, setIsHome] = useState<boolean>(false)
  const [isProductDesign, setIsProductDesign] = useState<boolean>(false)
  const [isIdeaToApp, setIsIdeaToApp] = useState<boolean>(false)

  const [isApps, setIsApps] = useState<boolean>(false)
  const [isAuth, setIsAuth] = useState<boolean>(false)
  const [isPages, setIsPages] = useState<boolean>(false)
  const [isBaseUi, setIsBaseUi] = useState<boolean>(false)
  const [isAdvanceUi, setIsAdvanceUi] = useState<boolean>(false)
  const [isForms, setIsForms] = useState<boolean>(false)
  const [isTables, setIsTables] = useState<boolean>(false)
  const [isCharts, setIsCharts] = useState<boolean>(false)
  const [isIcons, setIsIcons] = useState<boolean>(false)
  const [isMaps, setIsMaps] = useState<boolean>(false)
  const [isMultiLevel, setIsMultiLevel] = useState<boolean>(false)

  const [isLanding, setIsLanding] = useState<boolean>(false)

  const [iscurrentState, setIscurrentState] = useState('Dashboard')

  function updateIconSidebar (e: any) {
    if (e && e.target && e.target.getAttribute('sub-items')) {
      const ul: any = document.getElementById('two-column-menu')
      const iconItems: any = ul.querySelectorAll('.nav-icon.active')
      let activeIconItems = [...iconItems]
      activeIconItems.forEach((item: any) => {
        item.classList.remove('active')
        var id = item.getAttribute('sub-items')
        const getID = document.getElementById(id) as HTMLElement
        if (getID) getID.classList.remove('show')
      })
    }
  }

  useEffect(() => {
    document.body.classList.remove('twocolumn-panel')
    // if (iscurrentState !== 'Dashboard') {
    //     setIsDashboard(false);
    // }
    if (iscurrentState !== 'Home') {
      setIsHome(false)
    }
    if (iscurrentState !== 'Apps') {
      setIsApps(false)
    }
    if (iscurrentState !== 'Auth') {
      setIsAuth(false)
    }
    if (iscurrentState !== 'Pages') {
      setIsPages(false)
    }
    if (iscurrentState !== 'BaseUi') {
      setIsBaseUi(false)
    }
    if (iscurrentState !== 'AdvanceUi') {
      setIsAdvanceUi(false)
    }
    if (iscurrentState !== 'Forms') {
      setIsForms(false)
    }
    if (iscurrentState !== 'Tables') {
      setIsTables(false)
    }
    if (iscurrentState !== 'Charts') {
      setIsCharts(false)
    }
    if (iscurrentState !== 'Icons') {
      setIsIcons(false)
    }
    if (iscurrentState !== 'Maps') {
      setIsMaps(false)
    }
    if (iscurrentState !== 'MuliLevel') {
      setIsMultiLevel(false)
    }
    if (iscurrentState === 'Widgets') {
      history('/widgets')
      document.body.classList.add('twocolumn-panel')
    }
    if (iscurrentState !== 'Landing') {
      setIsLanding(false)
    }
  }, [
    history,
    iscurrentState,
    isDashboard,
    isApps,
    isAuth,
    isPages,
    isBaseUi,
    isAdvanceUi,
    isForms,
    isTables,
    isCharts,
    isIcons,
    isMaps,
    isMultiLevel
  ])

  const menuItems: any = [
    // {
    //   label: "Menu",
    //   isHeader: true,
    // },
    {
      id: 'home',
      label: 'Home',
      icon: 'ri-home-4-line',
      link: '/',
      stateVariables: isHome,
      click: function (e: any) {
        e.preventDefault()
        setIsHome(!isHome)
        setIscurrentState('Home')
        updateIconSidebar(e)
      }
      // subItems: [
      //   {
      //     id: "dashboard",
      //     label: "Dashboard",
      //     link: "/home",
      //     parentId: "home",
      //   },

      // ],
    },

    {
      id: 'product_design',
      label: 'Generate Wireframe',
      icon: 'ri-pencil-ruler-line',
      link: '/generate_wireframe',
      stateVariables: isProductDesign,
      click: function (e: any) {
        e.preventDefault()
        setIsProductDesign(!isProductDesign)
        setIscurrentState('Product Design')
        updateIconSidebar(e)
      }
      // subItems: [
      //   {
      //     id: "generate_wireframe",
      //     label: "Generate WireFrame",
      //     link: "/generate_wireframe",
      //     parentId: "product_design",
      //   },
      //   {
      //     id: "generate_web_design",
      //     label: "Generate Web Design",
      //     link: "/generate_web_design",
      //     parentId: "product_design",
      //   },
      //   {
      //     id: "wireframe_templates",
      //     label: "WireFrame Tamplates",
      //     link: "/wireframe_templates",
      //     parentId: "product_design",
      //   },
      //   {
      //     id: "my_wireframe",
      //     label: "My WireFrame",
      //     link: "/my_wireframe",
      //     parentId: "product_design",
      //   },
      // ],
    },
    {
      id: 'idea_to_app',
      label: 'Image to App',
      icon: 'ri-lightbulb-line',
      link: '/image_to_app',
      stateVariables: isIdeaToApp,
      click: function (e: any) {
        e.preventDefault()
        setIsIdeaToApp(!isIdeaToApp)
        setIscurrentState('Idea To App')
        updateIconSidebar(e)
      }
      // subItems: [
      //   {
      //     id: "develop_microsites",
      //     label: "Develop Microsites",
      //     link: "/develop_microsites",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "image_to_app",
      //     label: "Image to App",
      //     link: "/image_to_app",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "prompt_to_app",
      //     label: "Prompt to App",
      //     link: "/prompt_to_app",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "Showcase",
      //     label: "Showcase",
      //     link: "/showcase",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "my_apps",
      //     label: "My Apps",
      //     link: "/my_apps",
      //     parentId: "idea_to_app",
      //   },
      // ],
    },
    {
      id: 'design_analyser',
      label: 'Design Analyser',
      icon: 'ri-stack-fill',
      link: '/design_analyser',
      stateVariables: isIdeaToApp,
      click: function (e: any) {
        e.preventDefault()
        setIsIdeaToApp(!isIdeaToApp)
        setIscurrentState('Design Analyser')
        updateIconSidebar(e)
      }
      // subItems: [
      //   {
      //     id: "develop_microsites",
      //     label: "Develop Microsites",
      //     link: "/develop_microsites",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "image_to_app",
      //     label: "Image to App",
      //     link: "/image_to_app",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "prompt_to_app",
      //     label: "Prompt to App",
      //     link: "/prompt_to_app",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "Showcase",
      //     label: "Showcase",
      //     link: "/showcase",
      //     parentId: "idea_to_app",
      //   },
      //   {
      //     id: "my_apps",
      //     label: "My Apps",
      //     link: "/my_apps",
      //     parentId: "idea_to_app",
      //   },
      // ],
    }
  ]
  return <React.Fragment>{menuItems}</React.Fragment>
}
export default Navdata
