#!/bin/bash

# Create required directories
mkdir -p ./build/experience/discover/static

# Generate config.js
echo "window.runtime_config = {" > ./build/experience/discover/config.js
  
declare -A env_mapping=(
  ["REACT_APP_APP_REDIRECT_URI"]="APP_REDIRECT_URI"
  ["REACT_APP_API_AUTH_URL"]="API_AUTH_URL"
  ["REACT_APP_MLO_SERVER"]="MLO_SERVER"
  ["REACT_APP_CONTEXT_URL"]="CONTEXT_URL"
)

while IFS="=" read -r key value; do
  if [[ $key == REACT_APP_* ]]; then
    config_name=${env_mapping[$key]}
    if [[ ! -z "$config_name" ]]; then
      escaped_value=$(echo "$value" | sed 's/"/\\"/g')
      echo "  $config_name: \"$escaped_value\"," >> ./build/experience/discover/config.js
    fi
  fi
done < <(env)

echo "};" >> ./build/experience/discover/config.js

# Create manifest.json
echo '{ "name": "My App", "short_name": "App", "start_url": "/experience/discover/index.html", "display": "standalone" }' > ./build/experience/discover/manifest.json

# Move assets
mv ./build/static/* ./build/experience/discover/static/ 2>/dev/null || true
mv ./build/favicon.ico ./build/experience/discover/favicon.ico 2>/dev/null || true

# Create static web config
echo '{"rewrites":[{"source":"/experience/discover/**","destination":"/experience/discover/index.html"}]}' > ./build/staticwebapp.config.json

# Start server
exec serve -s build -l 4200 --single
