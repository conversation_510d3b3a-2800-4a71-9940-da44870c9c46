# ---- Stage 1: Build the React application ----
FROM node:23.11.0-alpine AS builder

RUN apk add --no-cache bash

WORKDIR /app

COPY package*.json ./

# Install all dependencies
RUN npm install --force

# Copy the rest of your application source code
COPY . .

# Build the application for production
RUN npm run build


# ---- Stage 2: Serve the application using 'serve' ----
FROM node:23.11.0-alpine AS runner

WORKDIR /app


# Install bash and serve globally
RUN apk add --no-cache bash \
    && npm install -g serve

# Copy ONLY the build output from the 'builder' stage
COPY --from=builder /app/build ./build

# Copy your start.sh script 

COPY ./start.sh ./start.sh
RUN chmod +x ./start.sh

# Expose the port your app will run on (defined in start.sh or serve command)
EXPOSE 4200

CMD ["bash", "./start.sh"]